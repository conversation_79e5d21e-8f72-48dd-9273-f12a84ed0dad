import React, { useState } from 'react';
import {
  Box,
  Button,
  Chip,
  Avatar,
  Typography,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Download as DownloadIcon,
  Visibility as ViewIcon,
  MoreVert as MoreVertIcon,
} from '@mui/icons-material';
import EnhancedDataTable from '../EnhancedDataTable';

// 示例数据
const sampleData = [
  {
    id: 1,
    name: 'sdfasfgadfgadf...',
    category: '图片',
    client: 'trailsub',
    resolution: '1920x1080',
    format: 'jpeg',
    size: '2.14MB',
    type: '/',
    status: 'active',
    actions: '操作',
  },
  {
    id: 2,
    name: 'lovely.doc111111',
    category: '图片',
    client: 'trailsub',
    resolution: '1600x707',
    format: 'jpg',
    size: '36.04KB',
    type: '男',
    status: 'active',
    actions: '操作',
  },
  // 更多示例数据...
];

// 表格列配置
const columns = [
  {
    field: 'name',
    headerName: '名称',
    minWidth: 200,
    renderCell: ({ value, row }) => (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <Avatar
          sx={{ width: 32, height: 32, fontSize: '12px' }}
          src={row.avatar}
        >
          {value?.charAt(0)?.toUpperCase()}
        </Avatar>
        <Typography variant="body2" sx={{ fontWeight: 500 }}>
          {value}
        </Typography>
      </Box>
    ),
  },
  {
    field: 'category',
    headerName: '分类',
    minWidth: 100,
  },
  {
    field: 'client',
    headerName: '客户名称',
    minWidth: 120,
    renderCell: ({ value }) => (
      <Typography variant="body2" color="primary" sx={{ fontWeight: 500 }}>
        {value}
      </Typography>
    ),
  },
  {
    field: 'resolution',
    headerName: '分辨率',
    minWidth: 120,
    renderCell: ({ value }) => (
      <Chip
        label={value}
        size="small"
        variant="outlined"
        sx={{
          borderRadius: '6px',
          fontSize: '11px',
          backgroundColor: '#F0F7FF',
          borderColor: '#1976D2',
          color: '#1976D2',
        }}
      />
    ),
  },
  {
    field: 'format',
    headerName: '格式',
    minWidth: 80,
    renderCell: ({ value }) => (
      <Typography
        variant="body2"
        sx={{
          backgroundColor: '#E3F2FD',
          color: '#1565C0',
          padding: '2px 8px',
          borderRadius: '4px',
          fontSize: '12px',
          fontWeight: 500,
        }}
      >
        {value?.toUpperCase()}
      </Typography>
    ),
  },
  {
    field: 'size',
    headerName: '大小',
    minWidth: 100,
    align: 'right',
  },
  {
    field: 'type',
    headerName: '性别',
    minWidth: 80,
    renderCell: ({ value }) => (
      value === '/' ? (
        <Typography variant="body2" color="text.disabled">
          -
        </Typography>
      ) : (
        <Chip
          label={value}
          size="small"
          color={value === '男' ? 'primary' : 'secondary'}
          sx={{ fontSize: '12px' }}
        />
      )
    ),
  },
  {
    field: 'status',
    headerName: '状态',
    minWidth: 100,
    type: 'status',
  },
  {
    field: 'actions',
    headerName: '操作',
    minWidth: 120,
    align: 'center',
    renderCell: ({ row }) => (
      <Box sx={{ display: 'flex', gap: 0.5 }}>
        <Tooltip title="查看">
          <IconButton
            size="small"
            sx={{
              color: '#52C41A',
              '&:hover': { backgroundColor: '#52C41A15' },
            }}
          >
            <ViewIcon fontSize="small" />
          </IconButton>
        </Tooltip>
        <Tooltip title="编辑">
          <IconButton
            size="small"
            sx={{
              color: '#1890FF',
              '&:hover': { backgroundColor: '#1890FF15' },
            }}
          >
            <EditIcon fontSize="small" />
          </IconButton>
        </Tooltip>
        <Tooltip title="删除">
          <IconButton
            size="small"
            sx={{
              color: '#FF4D4F',
              '&:hover': { backgroundColor: '#FF4D4F15' },
            }}
          >
            <DeleteIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </Box>
    ),
  },
];

const TableExample = () => {
  const [selectedRows, setSelectedRows] = useState([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [loading, setLoading] = useState(false);

  const handleSelectionChange = (newSelection) => {
    setSelectedRows(newSelection);
  };

  const handlePageChange = (event, newPage) => {
    setPage(newPage);
  };

  const handleRowsPerPageChange = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleRowClick = (row) => {
    console.log('Row clicked:', row);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h5" sx={{ mb: 3, fontWeight: 600 }}>
        增强型数据表格示例
      </Typography>
      
      <EnhancedDataTable
        columns={columns}
        rows={sampleData}
        loading={loading}
        checkboxSelection={true}
        selectedRows={selectedRows}
        onSelectionChange={handleSelectionChange}
        onRowClick={handleRowClick}
        pagination={true}
        page={page}
        rowsPerPage={rowsPerPage}
        totalRecords={sampleData.length}
        onPageChange={handlePageChange}
        onRowsPerPageChange={handleRowsPerPageChange}
        dense={false}
        stickyHeader={true}
        maxHeight="600px"
      />

      {selectedRows.length > 0 && (
        <Box sx={{ mt: 2, p: 2, backgroundColor: '#E3F2FD', borderRadius: 1 }}>
          <Typography variant="body2">
            已选择 {selectedRows.length} 项: {selectedRows.join(', ')}
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default TableExample;
