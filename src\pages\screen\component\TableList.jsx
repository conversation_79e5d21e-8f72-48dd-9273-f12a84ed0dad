import React, { useMemo, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import ZktecoTable from "@c/ZktecoTable";
import { useTranslation } from "react-i18next";
import { toast } from "react-toastify";
import { Grid, Tooltip, IconButton } from "@mui/material";
import BindIcon from "@a/images/bind_icon.svg?react";
// 导入自定义图标
import FilterListIcon from "@mui/icons-material/FilterList";
import SearchIcon from "@mui/icons-material/Search";
import SettingsIcon from "@mui/icons-material/Settings";
import ExportIcon from "@mui/icons-material/GetApp";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import VisibilityIcon from "@mui/icons-material/Visibility";
import {
  calculateBatteryStatus,
  getFormattedDate,
  handleDataBind,
} from "./utils";
import CommonUtil from "@/util/CommonUtils.js";
function TableList({
  data,
  isLoading,
  isRefetching,
  isError,
  rowCount,
  pagination,
  setPagination,
  getTableData,
}) {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const columns = useMemo(
    () => [
      {
        accessorKey: "id",
        header: t("screen.screenId"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },
      {
        accessorKey: "resolution",
        header: t("screen.screen_resolution"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },

      {
        accessorKey: "positionNO",
        header: t("screen.positionNo"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },

      {
        accessorKey: "screenName",
        header: t("screen.screenName"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },
      {
        accessorKey: "powerStatus",
        header: t("screen.powerStatus"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
        Cell: ({ row }) => {
          return <span>{calculateBatteryStatus(row.original?.battery)}</span>;
        },
      },
      {
        accessorKey: "deviceSn",
        header: t("screen.gateway"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },
      {
        accessorKey: "outletName",
        header: t("screen.outlet"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },

      {
        accessorKey: "productId",
        header: t("screen.bindToProduct"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },

      {
        accessorKey: "templateId",
        header: t("screen.outlet"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
        Cell: ({ row }) => {
          return (
            <Tooltip title={t("common.preview")} arrow>
              <Button
                id="temppreview"
                onClick={() => {
                  let templateJson = row?.original?.templateJson;

                  if (templateJson) {
                    setPreViewOpen(true);
                    setPreViewItem(row?.original);
                  } else {
                    toast.info(t("screen.unbound_templates"));
                  }
                }}>
                {t("common.preview")}
              </Button>
            </Tooltip>
          );
        },
      },

      {
        accessorKey: "screenStatus",
        header: t("screen.screenStatus"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
        Cell: ({ row }) => (
          <Tooltip
            title={
              row?.original?.status == "1"
                ? t("menu.online")
                : t("menu.offline")
            }
            arrow
            placement="bottom">
            <span
              style={{ color: row?.original?.status == "1" ? "green" : "red" }}>
              {row?.original?.status == "1"
                ? t("menu.online")
                : t("menu.offline")}
            </span>
          </Tooltip>
        ),
      },

      {
        accessorKey: "screenUpdateTime",
        header: t("screen.screenUpdateTime"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
        Cell: ({ row }) => (
          <Tooltip
            title={getFormattedDate(row?.original?.updatedAt)}
            arrow
            placement="bottom">
            <span>
              {CommonUtil.formatLongText(
                getFormattedDate(row?.original?.updatedAt)
              )}
            </span>
          </Tooltip>
        ),
      },
    ],
    []
  );

  const isShowAction = {
    isShowEditor: "nt:nutag:screen:update",
  };

  const handlePageChange = useCallback(
    (pageIndex) => {
      setPagination((prev) => ({ ...prev, pageIndex }));
    },
    [setPagination]
  );

  const handlePageSizeChange = useCallback(
    (pageSize) => {
      setPagination({ pageIndex: 0, pageSize });
    },
    [setPagination]
  );

  const actionHandlers = useMemo(
    () => ({
      handlerEditor: (data) =>
        navigate("/edit/screens", {
          state: { id: data?.id, type: "editor" },
        }),
    }),
    []
  );

  // 自定义顶部操作按钮
  const customTopActions = useMemo(
    () => [
      {
        icon: <DownloadIcons />,
        title: t("screen.import_binding_sheet"),
        tooltip: t("screen.import_binding_sheet"),
        onClick: () => {
          console.log("筛选操作");
          toast.info("筛选功能");
        },
        color: "#1976d2",
      },
      {
        icon: <ImportIcons />,
        title: "搜索",
        tooltip: "搜索数据",
        onClick: () => {
          console.log("搜索操作");
          toast.info("搜索功能");
        },
        color: "#2e7d32",
      },
      {
        icon: <SettingsIcon />,
        title: "设置",
        tooltip: "表格设置",
        onClick: () => {
          console.log("设置操作");
          toast.info("设置功能");
        },
        color: "#ed6c02",
      },
      {
        icon: <ExportIcon />,
        title: "导出",
        tooltip: "导出数据",
        onClick: () => {
          console.log("导出操作");
          toast.info("导出功能");
        },
        color: "#9c27b0",
      },
    ],
    []
  );

  // 自定义行操作渲染
  const renderCustomRowActions = useCallback(
    ({ row }) => {
      return (
        <Grid
          container
          spacing={1}
          sx={{ display: "flex", flexDirection: "row" }}>
          {/* 原有的绑定图标 */}
          {(row.original.isFree == 0 || row.original.subStatus == 3) && (
            <Grid item>
              <Tooltip title={t("screen.bindData")} arrow placement="bottom">
                <BindIcon
                  style={{
                    alignSelf: "center",
                    paddingTop: "0px",
                    cursor: "pointer",
                    opacity: "0.6",
                    height: "17px",
                    width: "20px",
                  }}
                  onClick={() => {
                    if (calculateBatteryStatus(row.original.battery) == "0%") {
                      toast.error("Screen battery is 0%");
                      return;
                    }
                    handleDataBind(row.original.id, data, navigate);
                  }}
                />
              </Tooltip>
            </Grid>
          )}

          {/* 新增的自定义操作图标 */}
          <Grid item>
            <Tooltip title="查看详情" arrow placement="bottom">
              <VisibilityIcon
                sx={{
                  cursor: "pointer",
                  color: "#1976d2",
                  fontSize: "18px",
                  "&:hover": { color: "#1565c0" },
                }}
                onClick={() => {
                  console.log("查看详情", row.original);
                  toast.info(`查看 ${row.original.screenName} 详情`);
                }}
              />
            </Tooltip>
          </Grid>

          <Grid item>
            <Tooltip title="快速编辑" arrow placement="bottom">
              <EditIcon
                sx={{
                  cursor: "pointer",
                  color: "#2e7d32",
                  fontSize: "18px",
                  "&:hover": { color: "#1b5e20" },
                }}
                onClick={() => {
                  console.log("快速编辑", row.original);
                  toast.info(`编辑 ${row.original.screenName}`);
                }}
              />
            </Tooltip>
          </Grid>

          <Grid item>
            <Tooltip title="删除" arrow placement="bottom">
              <DeleteIcon
                sx={{
                  cursor: "pointer",
                  color: "#d32f2f",
                  fontSize: "18px",
                  "&:hover": { color: "#c62828" },
                }}
                onClick={() => {
                  console.log("删除", row.original);
                  if (
                    window.confirm(`确定要删除 ${row.original.screenName} 吗？`)
                  ) {
                    toast.success("删除成功");
                  }
                }}
              />
            </Tooltip>
          </Grid>
        </Grid>
      );
    },
    [t, data, navigate]
  );

  return (
    <ZktecoTable
      columns={columns}
      data={data}
      rowCount={rowCount}
      isLoading={isLoading}
      isRefetching={isRefetching}
      isError={isError}
      pathRoute="/edit/screens"
      loadDada={getTableData}
      paginationProps={{
        currentPage: pagination.pageIndex,
        rowsPerPage: pagination.pageSize,
        onPageChange: handlePageChange,
        onPageSizeChange: handlePageSizeChange,
      }}
      topActions={{
        showAdd: false,
        customActions: customTopActions,
      }}
      getRowId={(originalRow) => {
        return originalRow?.id;
      }}
      renderRowActions={renderCustomRowActions}
      actionHandlers={actionHandlers}
      isShowAction={isShowAction}
    />
  );
}

export default TableList;
