import React, { useMemo } from "react";
import { useNavigate } from "react-router-dom";
import ZktecoTable from "@c/ZktecoTable";
import { useTranslation } from "react-i18next";
import { toast } from "react-toastify";
import BindIcon from "@a/images/bind_icon.svg?react";
import {
  calculateBatteryStatus,
  getFormattedDate,
  handleDataBind,
} from "./utils";
import CommonUtil from "@/util/CommonUtils.js";
function TableList({
  data,
  isLoading,
  isRefetching,
  isError,
  rowCount,
  pagination,
  setPagination,
  getTableData,
}) {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const columns = useMemo(
    () => [
      {
        accessorKey: "id",
        header: t("screen.screenId"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },
      {
        accessorKey: "resolution",
        header: t("screen.screen_resolution"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },

      {
        accessorKey: "positionNO",
        header: t("screen.positionNo"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },

      {
        accessorKey: "screenName",
        header: t("screen.screenName"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },
      {
        accessorKey: "powerStatus",
        header: t("screen.powerStatus"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
        Cell: ({ row }) => {
          return <span>{calculateBatteryStatus(row.original?.battery)}</span>;
        },
      },
      {
        accessorKey: "deviceSn",
        header: t("screen.gateway"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },
      {
        accessorKey: "outletName",
        header: t("screen.outlet"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },

      {
        accessorKey: "productId",
        header: t("screen.bindToProduct"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },

      {
        accessorKey: "templateId",
        header: t("screen.outlet"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
        Cell: ({ row }) => {
          return (
            <Tooltip title={t("common.preview")} arrow>
              <Button
                id="temppreview"
                onClick={() => {
                  let templateJson = row?.original?.templateJson;

                  if (templateJson) {
                    setPreViewOpen(true);
                    setPreViewItem(row?.original);
                  } else {
                    toast.info(t("screen.unbound_templates"));
                  }
                }}>
                {t("common.preview")}
              </Button>
            </Tooltip>
          );
        },
      },

      {
        accessorKey: "screenStatus",
        header: t("screen.screenStatus"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
        Cell: ({ row }) => (
          <Tooltip
            title={
              row?.original?.status == "1"
                ? t("menu.online")
                : t("menu.offline")
            }
            arrow
            placement="bottom">
            <span
              style={{ color: row?.original?.status == "1" ? "green" : "red" }}>
              {row?.original?.status == "1"
                ? t("menu.online")
                : t("menu.offline")}
            </span>
          </Tooltip>
        ),
      },

      {
        accessorKey: "screenUpdateTime",
        header: t("screen.screenUpdateTime"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
        Cell: ({ row }) => (
          <Tooltip
            title={getFormattedDate(row?.original?.updatedAt)}
            arrow
            placement="bottom">
            <span>
              {CommonUtil.formatLongText(
                getFormattedDate(row?.original?.updatedAt)
              )}
            </span>
          </Tooltip>
        ),
      },
    ],
    []
  );

  const isShowAction = {
    isShowEditor: "nt:nutag:screen:update",
  };

  const handlePageChange = useCallback(
    (pageIndex) => {
      setPagination((prev) => ({ ...prev, pageIndex }));
    },
    [setPagination]
  );

  const handlePageSizeChange = useCallback(
    (pageSize) => {
      setPagination({ pageIndex: 0, pageSize });
    },
    [setPagination]
  );

  const actionHandlers = useMemo(
    () => ({
      handlerEditor: (data) =>
        navigate("/edit/screens", {
          state: { id: data?.id, type: "editor" },
        }),
    }),
    []
  );

  return (
    <ZktecoTable
      columns={columns}
      data={data}
      rowCount={rowCount}
      isLoading={isLoading}
      isRefetching={isRefetching}
      isError={isError}
      pathRoute="/edit/screens"
      loadDada={getTableData}
      paginationProps={{
        currentPage: pagination.pageIndex,
        rowsPerPage: pagination.pageSize,
        onPageChange: handlePageChange,
        onPageSizeChange: handlePageSizeChange,
      }}
      topActions={{
        showAdd: false,
      }}
      getRowId={(originalRow) => {
        return originalRow?.id;
      }}
      renderRowActions={({ cell, row, table }) => {
        return (
          <Grid
            container
            spacing={2}
            sx={{
              display: "flex",
              flexDirection: "row",
            }}>
            {(row.original.isFree == 0 || row.original.subStatus == 3) && (
              <Grid item>
                <Tooltip title={t("screen.bindData")} arrow placement="bottom">
                  <BindIcon
                    style={{
                      alignSelf: "center",
                      paddingTop: "0px",
                      cursor: "pointer",
                      opacity: "0.6",
                      height: "17px",
                      width: "20px",
                    }}
                    onClick={() => {
                      calculateBatteryStatus(e.row.battery);
                      if (calculateBatteryStatus(e.row.battery) == "0%") {
                        toast.error("Screen battery is 0%");
                        return;
                      }
                      handleDataBind(e.row.id, records, navigate);
                    }}
                  />
                </Tooltip>
              </Grid>
            )}
          </Grid>
        );
      }}
      actionHandlers={actionHandlers}
      isShowAction={isShowAction}
    />
  );
}

export default TableList;
