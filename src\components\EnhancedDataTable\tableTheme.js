import { createTheme } from '@mui/material/styles';

// 表格专用主题配置
export const tableTheme = createTheme({
  palette: {
    primary: {
      main: '#1976D2',
      light: '#42A5F5',
      dark: '#1565C0',
    },
    secondary: {
      main: '#78BC27',
      light: '#8BC34A',
      dark: '#689F38',
    },
    success: {
      main: '#52C41A',
      light: '#73D13D',
      dark: '#389E0D',
    },
    warning: {
      main: '#FAAD14',
      light: '#FFC53D',
      dark: '#D48806',
    },
    error: {
      main: '#FF4D4F',
      light: '#FF7875',
      dark: '#CF1322',
    },
    background: {
      paper: '#FFFFFF',
      default: '#F5F5F5',
    },
    text: {
      primary: '#262626',
      secondary: '#8C8C8C',
      disabled: '#BFBFBF',
    },
    divider: '#F0F0F0',
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h6: {
      fontWeight: 600,
      fontSize: '1.1rem',
    },
    body1: {
      fontSize: '0.875rem',
    },
    body2: {
      fontSize: '0.8125rem',
    },
    caption: {
      fontSize: '0.75rem',
      color: '#8C8C8C',
    },
  },
  components: {
    MuiTableContainer: {
      styleOverrides: {
        root: {
          borderRadius: '8px',
          border: '1px solid #F0F0F0',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
        },
      },
    },
    MuiTableHead: {
      styleOverrides: {
        root: {
          '& .MuiTableCell-head': {
            backgroundColor: '#FAFAFA',
            color: '#595959',
            fontWeight: 600,
            fontSize: '0.8125rem',
            textTransform: 'uppercase',
            letterSpacing: '0.5px',
            borderBottom: '2px solid #F0F0F0',
            padding: '16px',
          },
        },
      },
    },
    MuiTableBody: {
      styleOverrides: {
        root: {
          '& .MuiTableRow-root': {
            '&:hover': {
              backgroundColor: '#F5F5F5',
            },
            '&.Mui-selected': {
              backgroundColor: '#E6F7FF',
              '&:hover': {
                backgroundColor: '#BAE7FF',
              },
            },
          },
          '& .MuiTableCell-root': {
            borderBottom: '1px solid #F0F0F0',
            padding: '12px 16px',
            fontSize: '0.875rem',
          },
        },
      },
    },
    MuiTablePagination: {
      styleOverrides: {
        root: {
          borderTop: '1px solid #F0F0F0',
          backgroundColor: '#FAFAFA',
          '& .MuiTablePagination-toolbar': {
            paddingLeft: '16px',
            paddingRight: '8px',
          },
          '& .MuiTablePagination-selectLabel, & .MuiTablePagination-displayedRows': {
            fontSize: '0.875rem',
            color: '#8C8C8C',
          },
        },
      },
    },
    MuiCheckbox: {
      styleOverrides: {
        root: {
          color: '#D9D9D9',
          '&.Mui-checked': {
            color: '#1976D2',
          },
          '&.MuiCheckbox-indeterminate': {
            color: '#1976D2',
          },
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: '6px',
          fontSize: '0.75rem',
          fontWeight: 500,
        },
        outlined: {
          borderWidth: '1px',
        },
        colorSuccess: {
          backgroundColor: '#F6FFED',
          color: '#52C41A',
          borderColor: '#B7EB8F',
        },
        colorWarning: {
          backgroundColor: '#FFFBE6',
          color: '#FAAD14',
          borderColor: '#FFE58F',
        },
        colorError: {
          backgroundColor: '#FFF2F0',
          color: '#FF4D4F',
          borderColor: '#FFCCC7',
        },
      },
    },
    MuiIconButton: {
      styleOverrides: {
        root: {
          borderRadius: '6px',
          padding: '6px',
          '&:hover': {
            backgroundColor: 'rgba(0, 0, 0, 0.04)',
          },
        },
        sizeSmall: {
          padding: '4px',
        },
      },
    },
    MuiTooltip: {
      styleOverrides: {
        tooltip: {
          backgroundColor: '#262626',
          fontSize: '0.75rem',
          borderRadius: '6px',
          padding: '6px 12px',
        },
        arrow: {
          color: '#262626',
        },
      },
    },
  },
});

// 状态颜色映射
export const statusColors = {
  active: {
    color: '#52C41A',
    backgroundColor: '#F6FFED',
    borderColor: '#B7EB8F',
  },
  inactive: {
    color: '#8C8C8C',
    backgroundColor: '#F5F5F5',
    borderColor: '#D9D9D9',
  },
  pending: {
    color: '#FAAD14',
    backgroundColor: '#FFFBE6',
    borderColor: '#FFE58F',
  },
  error: {
    color: '#FF4D4F',
    backgroundColor: '#FFF2F0',
    borderColor: '#FFCCC7',
  },
  success: {
    color: '#52C41A',
    backgroundColor: '#F6FFED',
    borderColor: '#B7EB8F',
  },
};

// 表格尺寸配置
export const tableSizes = {
  small: {
    cellPadding: '8px 12px',
    fontSize: '0.8125rem',
    rowHeight: 40,
  },
  medium: {
    cellPadding: '12px 16px',
    fontSize: '0.875rem',
    rowHeight: 48,
  },
  large: {
    cellPadding: '16px 20px',
    fontSize: '0.9375rem',
    rowHeight: 56,
  },
};

// 动画配置
export const tableAnimations = {
  hover: {
    transition: 'all 0.2s ease-in-out',
  },
  selection: {
    transition: 'background-color 0.3s ease',
  },
  loading: {
    transition: 'opacity 0.3s ease',
  },
};
