import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate, useLocation } from "react-router-dom";
import CustomInput from "@c/CustomInput";
import RightViewLayout from "@c/RighViewLayout";
import { list } from "@s/resolution.js";
import { getDetail, edit } from "@s/TemplateService.js";
import CommonUtil from "@u/CommonUtils.js";
import {
  Card,
  Grid,
  InputLabel,
  Autocomplete,
  TextField,
  Box,
  Button,
  Snackbar,
} from "@mui/material";
import { toast } from "react-toastify";
import { navigateWithToast } from "@/util/microFrontendNavigation";
function AddTemplate() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { state } = useLocation();
  const [image, setImage] = useState();
  const direction = [
    { id: "0", value: t("dictionary.vertical") },
    { id: "1", value: t("dictionary.horizontal") },
  ];

  let templateTypeOptions = [
    { id: "0", value: t("dictionary.generaluse") },
    { id: "1", value: t("dictionary.discount") },
    { id: "2", value: t("dictionary.byunit") },
    { id: "3", value: t("dictionary.by_value") },
    { id: "4", value: t("dictionary.promotion") },
  ];
  const [model, setModel] = useState([]);
  const [resolution, setResolution] = useState([]);
  const [modelList, setModelList] = useState([]);

  const myMap = new Map();

  const [payload, setPayload] = useState({
    name: "",
    orientation: null,
    resolution: null,
    size: "",
    model: null,
    promotionType: null,
  });
  const [error, setError] = useState({
    name: "",
    orientation: null,
    resolution: null,
    size: "",
    model: null,
    promotionType: null,
  });

  useEffect(() => {
    list().then((res) => {
      setResolution(res?.data?.valueList);
      setModelList(res?.data?.modelList);
    });

    if (state?.type == "editor") {
      getDetail(state?.id).then((res) => {
        const selectedDirection = direction.find(
          (item) => item.id == res?.data?.orientation
        );

        const selectedType = templateTypeOptions.find(
          (item) => item.id == res?.data?.promotionType
        );

        setPayload({
          ...payload,
          ...res.data,
          orientation: selectedDirection, // 默认选择第一个选项
          promotionType: selectedType,
        });
      });
    }
  }, []);

  useEffect(() => {
    for (let key in modelList) {
      if (modelList.hasOwnProperty(key)) {
        const modelArray = modelList[key].map((item) => item.model);
        myMap.set(key, modelArray);
      }
    }
  }, [myMap]);

  const validateField = (name, value) => {
    switch (name) {
      case "name":
        if (CommonUtil.isEmptyString(value)) {
          return t("tips.required");
        }
        if (value.length > 50) {
          return t("tips_template.characters");
        }
        return "";
      case "resolution":
        if (CommonUtil.isEmptyString(value)) {
          return t("tips.required");
        }
        return "";
      case "model":
        if (CommonUtil.isEmptyString(value)) {
          return t("tips.required");
        }
        return "";
      case "promotionType":
        if (CommonUtil.isEmptyString(value)) {
          return t("tips.required");
        }
        return "";
      default:
        return "";
    }
  };

  const handleChange = (event) => {
    const name = event.target.name;
    const value = event.target.value;

    setPayload({
      ...payload,
      [name]: value,
    });

    // Clear error for this field
    setError({
      ...error,
      [name]: "",
    });
  };

  const handleAutocompleteChange = (name, value) => {
    setPayload({
      ...payload,
      [name]: value,
    });

    // Clear error for this field
    setError({
      ...error,
      [name]: "",
    });

    // Special handling for resolution -> model dependency
    if (name === "resolution") {
      if (value !== null) {
        setModel(myMap.get(value));
      } else {
        setModel([]);
      }
      // Also clear model when resolution changes
      setPayload((prev) => ({
        ...prev,
        resolution: value ? value : "",
        model: null,
      }));
      setError((prev) => ({
        ...prev,
        resolution: "",
        model: "",
      }));
    }
  };

  const handleBlur = (name, value) => {
    const errorMessage = validateField(name, value);
    setError({
      ...error,
      [name]: errorMessage,
    });
  };

  const validateForm = () => {
    let isValid = true;
    const newErrors = { ...error };

    // Validate name
    if (CommonUtil.isEmptyString(payload.name)) {
      newErrors.name = t("tips.required");
      isValid = false;
    } else if (payload.name.length > 50) {
      newErrors.name = t("tips_template.characters");
      isValid = false;
    } else {
      newErrors.name = "";
    }

    // Validate resolution
    if (CommonUtil.isEmptyString(payload.resolution)) {
      newErrors.resolution = t("tips.required");
      isValid = false;
    } else {
      newErrors.resolution = "";
    }

    // Validate model
    if (CommonUtil.isEmptyString(payload.model)) {
      newErrors.model = t("tips.required");
      isValid = false;
    } else {
      newErrors.model = "";
    }

    // Validate promotionType
    if (CommonUtil.isEmptyString(payload.promotionType)) {
      newErrors.promotionType = t("tips.required");
      isValid = false;
    } else {
      newErrors.promotionType = "";
    }

    setError(newErrors);
    return isValid;
  };

  const handleSubmit = (e) => {
    if (validateForm()) {
      if (state?.type == "editor") {
        const name = payload.name;
        const resolution = payload.resolution;
        const orientation = payload.orientation?.id;
        const model = payload.model;
        const type = payload.promotionType?.id;
        const templateObjectKey = payload.templateObjectKey;
        const templateImage = image;
        const id = payload.id;
        const imageObjectKey = payload.imageDownloadObjectKey;

        const templateJson = payload.templateJson;

        navigate("/editor", {
          state: {
            name,
            resolution,
            orientation,
            model,
            type,
            templateObjectKey,
            templateImage,
            id,
            imageObjectKey,
            templateJson,
            isEditor: true,
          },
        });
      } else {
        const name = payload.name;
        const resolution = payload.resolution;
        const orientation = payload.orientation?.id;
        const model = payload.model;
        const type = payload.promotionType?.id;

        navigate("/editor", {
          state: {
            name,
            resolution,
            orientation,
            model,
            type,
            isEditor: false,
          },
        });
      }
    }
  };

  const handleSave = (e) => {
    if (validateForm()) {
      const params = {
        ...payload,
        orientation: payload.orientation.id
          ? payload.orientation.id
          : payload.orientation,
        promotionType: payload.promotionType.id
          ? payload.promotionType.id
          : payload.promotionType,
      };

      edit(state?.id, params).then((res) => {
        if (res?.code == "00000000") {
          navigate("/template");
          toast.success(res.message);
        } else {
          toast.error(res.message);
        }
      });
    }
  };

  return (
    <React.Fragment>
      <RightViewLayout
        id="addtempback"
        title={
          state?.type == "editor"
            ? t("template.edit_template")
            : t("template.add_template")
        }
        navigateBack={"/template"}
        isShowSearch={true}>
        <Card elevation={0} sx={{ height: "100%" }}>
          <Grid container p={3}>
            <Grid container xs={12} md={12} item spacing={2}>
              <Grid item md={6} xs={12}>
                <CustomInput
                  id="AddTemplate1"
                  required
                  label={t("template.name")}
                  size="small"
                  name="name"
                  value={payload.name}
                  error={!!error.name}
                  resetError={() => setError({ ...error, name: "" })}
                  inputProps={{
                    maxLength: 50,
                  }}
                  handleChange={handleChange}
                  onBlur={(e) => handleBlur("name", e.target.value)}
                  helperText={error.name || " "}
                  placeholder={t("tips_template.placeholder_name")}
                />
              </Grid>

              <Grid item md={6} xs={12}>
                <InputLabel
                  shrink
                  htmlFor="bootstrap-input"
                  style={{ paddingLeft: "0px" }}>
                  {t("template.resolution")}{" "}
                  <span style={{ color: "red" }}>*</span>
                </InputLabel>
                <Autocomplete
                  id="AddTemplateResolution"
                  options={resolution}
                  getOptionLabel={(option) => option}
                  value={payload.resolution}
                  onChange={(e, v) => handleAutocompleteChange("resolution", v)}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      name="resolution"
                      size="small"
                      error={!!error.resolution}
                      helperText={error.resolution || " "}
                      placeholder={t("tips_template.placeholder_resolution")}
                      sx={{
                        "& .MuiOutlinedInput-input.MuiInputBase-inputSizeSmall":
                          {
                            fontSize: "13px",
                            padding: "12px",
                          },
                      }}
                    />
                  )}
                />
              </Grid>

              <Grid item md={6} xs={12}>
                <InputLabel
                  shrink
                  htmlFor="bootstrap-input"
                  style={{ paddingLeft: "0px" }}>
                  {t("template.screen_model")}{" "}
                  <span style={{ color: "red" }}>*</span>
                </InputLabel>

                <Autocomplete
                  id="AddTemplateModel"
                  noOptionsText={t("tips.no_options")}
                  options={model}
                  value={payload.model}
                  getOptionLabel={(option) => option}
                  onChange={(e, v) => handleAutocompleteChange("model", v)}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      name="screenModel"
                      size="small"
                      error={!!error.model}
                      helperText={error.model || " "}
                      placeholder={t("tips_template.placeholder_screen_model")}
                      sx={{
                        "& .MuiOutlinedInput-input.MuiInputBase-inputSizeSmall":
                          {
                            fontSize: "13px",
                            padding: "12px",
                          },
                      }}
                    />
                  )}
                />
              </Grid>

              <Grid item md={6} xs={12}>
                <InputLabel
                  shrink
                  htmlFor="bootstrap-input"
                  style={{ paddingLeft: "0px" }}>
                  {t("template.screen_direction")}
                </InputLabel>
                <Autocomplete
                  id="AddTemplateDirection"
                  options={direction}
                  getOptionLabel={(option) => option.value || ""}
                  value={payload.orientation}
                  onChange={(e, v) =>
                    handleAutocompleteChange("orientation", v)
                  }
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      name="orientation"
                      size="small"
                      placeholder={t(
                        "tips_template.placeholder_screen_direction"
                      )}
                      sx={{
                        "& .MuiOutlinedInput-input.MuiInputBase-inputSizeSmall":
                          {
                            fontSize: "13px",
                            padding: "12px",
                          },
                      }}
                    />
                  )}
                />
              </Grid>

              <Grid item md={6} xs={12}>
                <InputLabel
                  shrink
                  htmlFor="bootstrap-input"
                  style={{ paddingLeft: "0px" }}>
                  {t("template.type")} <span style={{ color: "red" }}>*</span>
                </InputLabel>
                <Autocomplete
                  id="AddTemplateType"
                  options={templateTypeOptions}
                  getOptionLabel={(option) => option.value || ""}
                  value={payload.promotionType}
                  onChange={(e, v) =>
                    handleAutocompleteChange("promotionType", v)
                  }
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      name="type"
                      size="small"
                      error={!!error.promotionType}
                      helperText={error.promotionType || " "}
                      placeholder={t("tips_template.placeholder_type")}
                      sx={{
                        "& .MuiOutlinedInput-input.MuiInputBase-inputSizeSmall":
                          {
                            fontSize: "13px",
                            padding: "12px",
                          },
                      }}
                    />
                  )}
                />
              </Grid>

              <Grid container md={12} xs={12}>
                <Box
                  display={"flex"}
                  flexDirection={"row-reverse"}
                  style={{ marginTop: "30px", width: "100%" }}>
                  <Box item>
                    <Button
                      id="addtempnext"
                      variant="contained"
                      size="large"
                      className="text-transform-none"
                      onClick={handleSubmit}
                      style={{
                        size: "medium",
                        background:
                          "transparent linear-gradient(270deg, #1487CA 0%, #78BC27 100%) 0% 0% no-repeat padding-box",
                        borderRadius: "8px",
                        opacity: 1,
                      }}>
                      {t("common.next")}
                    </Button>
                  </Box>
                  {state?.type == "editor" && (
                    <Box item mr={2}>
                      <Button
                        id="addtempnext"
                        variant="outlined"
                        size="large"
                        className="text-transform-none"
                        onClick={handleSave}>
                        {t("common.save")}
                      </Button>
                    </Box>
                  )}

                  <Box item mr={2}>
                    <Button
                      id="addtempcan"
                      className="text-transform-none"
                      variant="outlined"
                      onClick={() => navigate("/template")}
                      size="large">
                      {t("common.cancel")}
                    </Button>
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </Grid>
        </Card>
      </RightViewLayout>
    </React.Fragment>
  );
}

export default AddTemplate;
