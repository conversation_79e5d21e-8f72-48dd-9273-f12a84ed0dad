import { tableI18n } from "@/util/tableLang";

import { dateFormat } from "@/util/utils";
import ZkDicShow from "@c/ZkDicShow";
// material-ui
import { styled } from "@mui/material/styles";
import { Box, Grid, Switch, Paper } from "@mui/material";
import Tooltip, { tooltipClasses } from "@mui/material/Tooltip";
import AuthButton from "./AuthButton";
import i18n from "i18next";
// import IconFont from "@c/IconFont";
import {
  MaterialReactTable,
  MRT_ToggleFullScreenButton,
  MRT_ToggleGlobalFilterButton,
  MRT_ShowHideColumnsButton,
  MRT_TablePagination,
  MRT_ToggleDensePaddingButton,
  useMaterialReactTable,
} from "material-react-table";

const HtmlTooltip = styled(({ className, ...props }) => (
  <Tooltip {...props} classes={{ popper: className }} />
))(({ theme }) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: "#ffffff",
    color: "#ffffff",
    padding: "10px",
    maxWidth: "90%",
    margin: "0px auto",
    fontSize: theme.typography.pxToRem(12),
    border: "1px solid #dadde9",
  },
}));

const ZktecoTable = (props) => {
  const {
    mrt = true,
    renderToolbarInternalActions = () => null, //头部右侧
    renderTopToolbarCustomActions = () => null, //头部左侧
    renderBottomToolbarCustomActions = () => null, //底部左侧工具
    enablePagination = true,
    minHeight = "70vh",
    maxHeight = "calc(100vh - 260px )",
    columns,
    state,
    data,
    enableColumnResizing = false,
    showTopBar = true,
    ...orther
  } = props;

  if (data && data.length > 0) {
    if (state && state.isLoading !== undefined) {
      state.isLoading = state.isLoading;
      state.showProgressBars = state.isLoading;
    }
  } else if (state) {
    state.showProgressBars = false;
  }

  let columnsConfig = columns.map((item) => {
    if (!item.enableColumnActions) {
      item.enableColumnActions = false;
    }
    if (!item.enableSorting) {
      item.enableSorting = false;
    }
    if (item.format) {
      item.Cell = ({ cell, row }) => {
        let value = row.original[item.accessorKey];
        if (value === null || value === "" || value === undefined) {
          return "-";
        }
        let type = typeof item.format;
        if (type === "boolean") {
          return dateFormat(row.original[item.accessorKey]);
        } else if (type === "function") {
          return item.format({ cell, row });
        } else if (type === "string") {
          return dateFormat(row.original[item.accessorKey], item.format);
        } else {
          return row.original[item.accessorKey];
        }
      };
    } else if (item.zkDic) {
      item.Cell = ({ cell, row }) => {
        let value = row.original[item.accessorKey];
        if (value === null || value === "" || value === undefined) {
          return "-";
        }
        return (
          <>
            <ZkDicShow
              type={item.zkDic}
              value={row.original[item.accessorKey]}
            />
          </>
        );
      };
    } else if (item.tooltip) {
      item.Cell = ({ cell, row }) => {
        let value = row.original[item.accessorKey];
        if (value === null || value === "" || value === undefined) {
          return "-";
        }

        if (value.length > item.tooltip) {
          return (
            <HtmlTooltip
              title={
                <Paper
                  sx={{
                    border: "none",
                    boxShadow: "none",
                  }}
                >
                  {value}
                </Paper>
              }
            >
              <Box
                sx={{
                  width: (item.size || 200) + "px",
                  overflow: "hidden",
                  whiteSpace: "nowrap",
                  textOverflow: "ellipsis",
                }}
              >
                {value}
              </Box>
            </HtmlTooltip>
          );
        } else {
          return value;
        }
      };
    } else if (item.switch) {
      let valueConfig = {
        trueValue: true,
        falseValue: false,
        ...item.valueConfig,
      };

      item.Cell = ({ cell, row }) => {
        let value = row.original[item.accessorKey];
        if (value === null || value === "" || value === undefined) {
          return "-";
        }
        return (
          <>
            <AuthButton
              message={
                <Switch
                  disabled={true}
                  onChange={(e) => {
                    e.stopPropagation();
                    item.switch(row.original);
                  }}
                  checked={
                    row.original[item.accessorKey] == valueConfig.trueValue
                  }
                />
              }
              id={item.authId}
            >
              <Switch
                onChange={(e) => {
                  e.stopPropagation();
                  item.switch(row.original);
                }}
                checked={
                  row.original[item.accessorKey] == valueConfig.trueValue
                }
              />
            </AuthButton>
          </>
        );
      };
    } else {
      if (!item.Cell) {
        item.Cell = ({ cell, row }) => {
          let value = row.original[item.accessorKey];
          if (value === null || value === "" || value === undefined) {
            return "-";
          }
          return value;
        };
      }
    }
    return item;
  });

  let defaultConfig = {
    localization: tableI18n,
    enablePagination: false, //是否开启分页
    muiTablePaperProps: {
      elevation: 0,
      sx: {
        borderRadius: "5px",
        minHeight: minHeight,
        backgroundColor: "#ffffff",
        display: "flex",
        flexDirection: "column",
        flexGrow: 1,
        justifyContent: "space-between",
      },
    },
    muiTableHeadProps: {
      sx: {
        top: "0px",
      },
    },
    muiTableHeadRowProps: {
      sx: {
        backgroundColor: "#e4e5e5",
        height: "60px",
        boxShadow: "none",
        "& .MuiTableCell-head": {
          height: "39px",
          lineHeight: "39px",
          fontSize: "16px",
          color: "#474B4F",
          padding: "10px",
          position: "sticky",
        },
      },
    },
    muiPaginationProps: {
      //分页滚动条
      color: "secondary",
      rowsPerPageOptions: [4, 10, 20, 30],
      shape: "rounded",
      variant: "outlined",
      className: "table_Pagination_cla",
    },

    muiTableFooterProps: {
      sx: {
        outline: "none",
      },
    },

    enableStickyHeader: true,
    enableStickyFooter: true,

    muiTableContainerProps: { sx: { maxHeight: maxHeight } },

    positionToolbarAlertBanner: "none",
    // 解决列太多宽度太长问题
    enableColumnResizing: enableColumnResizing,
    manualFiltering: true,
    // 布局方式
    layoutMode: "table",

    manualPagination: true,
    enableDensityToggle: true,
    manualSorting: true,

    // 设置背景颜色
    muiTableBodyCellProps: { sx: { backgroundColor: "white" } },

    muiTableProps: {
      sx: {
        backgroundColor: "white",
      },
    },

    muiBottomToolbarProps: {
      sx: {
        backgroundColor: "white",
        boxShadow: "none",
      },
    },

    muiTopToolbarProps: { sx: { backgroundColor: "white" } },

    positionActionsColumn: "last",

    paginationDisplayMode: "pages",

    enableBottomToolbar: true, //页面底部

    displayColumnDefOptions: {
      "mrt-row-actions": {
        header: i18n.t("components.ZktecoTable.203506-0"),
        muiTableHeadCellProps: {
          sx: {
            position: "relative",
          },
        },
      },
    },
    initialState: {
      columnPinning: {
        right: ["mrt-row-actions"],
      },
    },
    ...orther,
    columns: columnsConfig || [],
    state,
    data: data || [],
    renderCaption: ({ table }) => {
      //分页栏上面添加一栏
      return null;
    },
    enableTopToolbar: showTopBar,
    renderToolbarInternalActions: ({ table }) => {
      if (showTopBar) {
        return (
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
            }}
          >
            {renderToolbarInternalActions && (
              <Grid
                sx={{
                  marginRight: "20px",
                }}
              >
                {renderToolbarInternalActions({ table })}
              </Grid>
            )}
            {mrt && (
              <>
                <MRT_ShowHideColumnsButton table={table} />
                <MRT_ToggleDensePaddingButton table={table} />
                <MRT_ToggleFullScreenButton table={table} />
              </>
            )}
          </Box>
        );
      } else {
        return (
          <Grid
            sx={{
              height: "2px",
              backgroundColor: "red",
            }}
          ></Grid>
        );
      }
    },
    renderEmptyRowsFallback: ({ table }) => (
      <Box
        sx={{
          padding: "40px",
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          alignContent: "center",
          minHeight: "400px",
        }}
      >
        {/* <Grid
          sx={{
            display: "flex",
            justifyContent: "center",
            alignContent: "center",
          }}
        >
          <IconFont
            type="icon-Subtraction307"
            style={{ fontSize: "5.2rem", color: "#898787e8" }}
          />
        </Grid> */}
        <Grid
          sx={{
            display: "flex",
            justifyContent: "center",
            alignContent: "center",
            mt: 2,
          }}
        >
          {tableI18n.noRecordsToDisplay}
        </Grid>
      </Box>
    ),
    renderTopToolbarCustomActions: ({ table }) => {
      // return null;
      return (
        <Box
          sx={{
            alignItems: "center",
            boxSizing: "border-box",
            display: "flex",
            justifyContent: "space-between",
            width: "100%",
          }}
        >
          {renderTopToolbarCustomActions && (
            <Box>{renderTopToolbarCustomActions({ table })}</Box>
          )}
        </Box>
      );
    },
    //Adding a custom button to the bottom toolbar
    renderBottomToolbarCustomActions: ({ table }) => {
      return (
        <Box
          sx={{
            alignItems: "center",
            boxSizing: "border-box",
            display: "flex",
            justifyContent: "space-between",
            width: "100%",
          }}
        >
          {renderBottomToolbarCustomActions && (
            <Box>{renderBottomToolbarCustomActions({ table })}</Box>
          )}
          <Box
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              position: "relative",
              right: 0,
              top: 0,
            }}
          >
            {enablePagination && (
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "flex-end",
                }}
              >
                <Grid sx={{ color: "#595959", marginRight: "10px" }}>
                  {tableI18n.total?.replace("{total}", String(props.rowCount))}
                  {/* 共 <span style={{ margin: "0px 6px" }}>{props.rowCount}</span>
                  条记录 */}
                </Grid>
                <MRT_TablePagination
                  rowsPerPageOptions={[5, 10, 20, 30, 50, 100]}
                  sx={{
                    padding: "0",
                  }}
                  table={table}
                />
              </Box>
            )}
          </Box>
        </Box>
      );
    },
  };
  const table = useMaterialReactTable(defaultConfig);
  return <MaterialReactTable table={table} />;
};
export default ZktecoTable;
