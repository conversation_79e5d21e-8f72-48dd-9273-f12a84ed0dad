import { useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { setMenuList, activeItem } from "../reducers/menu";
import { setPermission } from "../reducers/user";
export const useMenuInfo = () =>
  useSelector((store) => {
    return store.menu.menuList;
  });

export const useActiveItem = () =>
  useSelector((store) => {
    return store.menu.openItem;
  });

export function useDispatchMenu() {
  const dispatch = useDispatch();
  const stateSetMenu = useCallback(
    (useMenu) => dispatch(setMenuList(useMenu)),
    [dispatch]
  );
  const stateSetPermission = useCallback(
    (permission) => dispatch(setPermission(permission)),
    [dispatch]
  );
  return { stateSetMenu, stateSetPermission };
}
