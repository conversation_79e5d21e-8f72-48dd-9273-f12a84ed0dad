import React, { useEffect, useState } from "react";
import {
  InputLabel,
  Stack,
  Autocomplete,
  TextField,
  FormHelperText,
} from "@mui/material";
import RequirePoint from "../RequirePoint";
import { useTranslation } from "react-i18next";

function CupAutoComplete({
  formik = null,
  placeholder = "",
  label = "",
  name = "",
  error = null,
  disabled = false,
  options = [],
  typevalue = "0", // 保持向后兼容
  labelpostion = "column",
  spacing = 1,
  width = "100%",
  fontSize = "18px",
  readonly = false,
  required = false,
  // 新增自定义字段名配置
  fieldNames = {
    label: "value", // 默认显示字段
    value: "id", // 默认值字段
    key: "id", // 默认唯一标识字段
  },
  // 新增选项类型，用于自动检测数据类型
  optionType = "auto", // 'auto' | 'object' | 'string' | 'number'
  ...otherProps
}) {
  const [data, setData] = useState(null);
  const { t } = useTranslation();

  // 智能检测数据类型
  const detectOptionType = () => {
    if (optionType !== "auto") return optionType;
    if (!options || options.length === 0) return "string";

    const firstOption = options[0];
    if (typeof firstOption === "string" || typeof firstOption === "number") {
      return "primitive";
    }
    return "object";
  };

  const currentOptionType = detectOptionType();

  // 获取选项的显示标签
  const getOptionLabel = (option) => {
    if (!option) return "";

    // 向后兼容旧的 typevalue 逻辑
    if (typevalue !== "0") {
      const mapping = {
        1: option,
        2: option?.id,
        3: option?.value,
        4: option?.label,
        5: option?.name,
      };
      return String(mapping[typevalue] || option?.value || "");
    }

    // 新的 fieldNames 逻辑
    if (currentOptionType === "primitive") {
      return String(option);
    }

    // 对象类型，使用 fieldNames 配置
    const labelField = fieldNames.label;
    return String(
      option?.[labelField] ||
        option?.value ||
        option?.label ||
        option?.name ||
        ""
    );
  };

  // 获取选项的值
  const getOptionValue = (option) => {
    if (!option) return null;

    // 向后兼容旧的 typevalue 逻辑
    if (typevalue === "1") {
      return option;
    }

    if (currentOptionType === "primitive") {
      return option;
    }

    // 对象类型，使用 fieldNames 配置
    const valueField = fieldNames.value;
    return option?.[valueField] || option?.id || option?.value;
  };

  // 获取选项的唯一标识
  const getOptionKey = (option) => {
    if (!option) return null;

    if (currentOptionType === "primitive") {
      return option;
    }

    const keyField = fieldNames.key;
    return option?.[keyField] || option?.id || option?.value;
  };

  const handleChange = (_, newValue) => {
    setData(newValue);
    const valueToSet = getOptionValue(newValue);
    formik?.setFieldValue(name, valueToSet);
  };

  // 根据当前值查找对应的选项
  const findOptionByValue = (value) => {
    if (!value && value !== 0) return null;

    return options.find((option) => {
      const optionValue = getOptionValue(option);
      return optionValue == value; // 使用 == 进行宽松比较，处理字符串和数字的情况
    });
  };

  useEffect(() => {
    const currentValue = formik?.values?.[name];
    const foundOption = findOptionByValue(currentValue);
    setData(foundOption || null);
  }, [formik?.values?.[name], options]);

  return (
    <Stack spacing={1} sx={{ width: "100%" }}>
      {label && (
        <Stack
          direction={labelpostion === "left" ? "row" : "column"}
          alignItems={labelpostion === "left" ? "flex-start" : ""}>
          <InputLabel
            htmlFor={`CmpAutoComPlete_${name}`}
            shrink
            sx={{
              fontSize: "18px",
              color: "#474b4fcc",
            }}>
            {label} {required && <RequirePoint />}
          </InputLabel>
        </Stack>
      )}
      <Stack
        sx={{
          flexGrow: 1,
          width: "100%",
          borderRadius: "15px",
        }}>
        <Autocomplete
          id={`CmpAutoComPlete_${name}`}
          disablePortal
          fullWidth
          options={options || []}
          value={data || null}
          label={""}
          onChange={handleChange}
          // 添加 onBlur 处理以确保 touched 状态被设置
          onBlur={() => formik?.setFieldTouched(name, true)}
          disabled={disabled}
          isOptionEqualToValue={(option, value) => {
            return getOptionKey(option) === getOptionKey(value);
          }}
          getOptionLabel={(option) => getOptionLabel(option)}
          noOptionsText={t("No Options")}
          renderInput={(params) => (
            <TextField
              {...params}
              readOnly={readonly}
              placeholder={placeholder}
              // 添加错误状态到 TextField
              error={
                Boolean(formik?.touched[name] && formik?.errors[name]) ||
                Boolean(error)
              }
              sx={{
                "& .MuiOutlinedInput-input": {
                  height: "35px",
                  borderRadius: "25px",
                  fontSize: "14px",
                  marginTop: "-5px",
                },
                "& .MuiInputBase-root ": {
                  height: "46px",
                  marginTop: "-5px",
                },
              }}
            />
          )}
          {...otherProps}
        />

        {/* 修改错误显示条件，在表单提交时也显示错误 */}
        {formik?.touched[name] && formik?.errors[name] && (
          <FormHelperText error id={`standard-weight-helper-text-${name}`}>
            {formik?.errors[name]}
          </FormHelperText>
        )}
      </Stack>
    </Stack>
  );
}

export default CupAutoComplete;
