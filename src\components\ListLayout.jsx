import React from "react";
import { Grid, Typography } from "@mui/material";
import _ from "lodash-es";
import SearchIcon from "@mui/icons-material/Search";
import ClearIcon from "@mui/icons-material/Clear";
import { t } from "i18next";
function LayoutList(props) {
  const {
    title,
    header,
    content,
    isSearch,
    serchName,
    onClick = () => {},

    setSeachName,
    onClear, // 新增 onClear 属性
    disabled,
    ...orther
  } = props;

  return (
    <React.Fragment>
      <Grid
        sx={{
          height: "93%",
          flexGrow: 1,
          background: ` #FFFFFF 0% 0% no-repeat padding-box`,
          boxShadow: "0px 0px 10px rgba(0, 0, 0, 0.1)",
          boder: "1px solid rgba(0, 0, 0, 0.1)",
        }}>
        {content}
      </Grid>
    </React.Fragment>
  );
}

export default LayoutList;
