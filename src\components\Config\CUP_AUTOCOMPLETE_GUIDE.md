# CupAutoComplete 组件使用指南

## 概述

`CupAutoComplete` 组件已经升级，现在支持多种数据类型和自定义字段名配置，可以灵活处理各种不同结构的选项数据。

## 新增功能

### ✨ 自定义字段名 (fieldNames)
可以自定义选项对象中用于显示、取值和唯一标识的字段名。

### 🔍 智能类型检测 (optionType)
自动检测选项数据类型，支持对象、字符串、数字等多种类型。

### 🔄 向后兼容
完全兼容原有的 `typevalue` 属性，无需修改现有代码。

## 属性配置

### 新增属性

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `fieldNames` | Object | `{label: 'value', value: 'id', key: 'id'}` | 自定义字段名配置 |
| `optionType` | String | `'auto'` | 选项类型: 'auto', 'object', 'primitive' |

### fieldNames 配置说明

```javascript
fieldNames = {
  label: 'value',  // 用于显示的字段名
  value: 'id',     // 用于取值的字段名  
  key: 'id'        // 用于唯一标识的字段名
}
```

## 使用示例

### 1. 标准对象数组 (id + value)

```javascript
const templateTypeOptions = [
  { id: "0", value: "通用" },
  { id: "1", value: "折扣" },
  { id: "2", value: "促销" },
];

<CupAutoComplete
  formik={formik}
  name="templateType"
  label="模板类型"
  options={templateTypeOptions}
  // 使用默认配置，无需额外设置
/>
```

### 2. 字符串数组

```javascript
const resolutions = [
  "200*200",
  "250*128", 
  "296*128",
  "320*240"
];

<CupAutoComplete
  formik={formik}
  name="resolution"
  label="分辨率"
  options={resolutions}
  // 自动检测为原始类型
/>
```

### 3. 自定义字段名 - label 字段

```javascript
const labelOptions = [
  { id: "0", label: "通用标签" },
  { id: "1", label: "折扣标签" },
  { id: "2", label: "促销标签" },
];

<CupAutoComplete
  formik={formik}
  name="labelField"
  label="标签选择"
  options={labelOptions}
  fieldNames={{
    label: 'label',  // 使用 label 字段显示
    value: 'id',     // 使用 id 字段取值
    key: 'id'        // 使用 id 字段作为唯一标识
  }}
/>
```

### 4. 自定义字段名 - name 字段

```javascript
const nameOptions = [
  { id: "0", name: "通用名称" },
  { id: "1", name: "折扣名称" },
  { id: "2", name: "促销名称" },
];

<CupAutoComplete
  formik={formik}
  name="nameField"
  label="名称选择"
  options={nameOptions}
  fieldNames={{
    label: 'name',   // 使用 name 字段显示
    value: 'id',     // 使用 id 字段取值
    key: 'id'        // 使用 id 字段作为唯一标识
  }}
/>
```

### 5. 完全自定义键值

```javascript
const customOptions = [
  { value: "0", name: "自定义项1" },
  { value: "1", name: "自定义项2" },
  { value: "2", name: "自定义项3" },
];

<CupAutoComplete
  formik={formik}
  name="customField"
  label="自定义选择"
  options={customOptions}
  fieldNames={{
    label: 'name',   // 使用 name 字段显示
    value: 'value',  // 使用 value 字段取值
    key: 'value'     // 使用 value 字段作为唯一标识
  }}
/>
```

### 6. 数字数组

```javascript
const numberOptions = [1, 2, 3, 4, 5];

<CupAutoComplete
  formik={formik}
  name="numberValue"
  label="数字选择"
  options={numberOptions}
  // 自动检测为原始类型
/>
```

## 向后兼容

原有的 `typevalue` 属性仍然完全支持：

```javascript
// 旧方式 - 仍然有效
<CupAutoComplete
  typevalue="1"  // 字符串类型
  options={stringArray}
/>

<CupAutoComplete
  typevalue="2"  // 使用 id 字段
  options={objectArray}
/>

<CupAutoComplete
  typevalue="3"  // 使用 value 字段
  options={objectArray}
/>
```

## 智能类型检测

组件会自动检测选项数据类型：

- **原始类型**: 字符串、数字数组
- **对象类型**: 包含属性的对象数组

你也可以手动指定类型：

```javascript
<CupAutoComplete
  optionType="primitive"  // 强制指定为原始类型
  options={["option1", "option2"]}
/>

<CupAutoComplete
  optionType="object"     // 强制指定为对象类型
  options={[{id: 1, name: "option1"}]}
/>
```

## 最佳实践

### 1. 推荐使用新的 fieldNames 配置
```javascript
// ✅ 推荐 - 清晰明确
fieldNames={{
  label: 'name',
  value: 'id', 
  key: 'id'
}}

// ❌ 不推荐 - 不够直观
typevalue="5"
```

### 2. 保持字段名一致性
在同一个项目中，尽量保持相同类型数据的字段名一致。

### 3. 利用自动检测
大多数情况下，让组件自动检测数据类型即可，无需手动指定 `optionType`。

### 4. 处理复杂数据结构
```javascript
const complexOptions = [
  { 
    uuid: "abc-123", 
    displayName: "选项1", 
    internalValue: "opt1" 
  }
];

<CupAutoComplete
  fieldNames={{
    label: 'displayName',
    value: 'internalValue',
    key: 'uuid'
  }}
  options={complexOptions}
/>
```

## 迁移指南

### 从旧版本迁移

1. **无需修改现有代码** - 所有现有的 `typevalue` 配置都会继续工作
2. **逐步迁移** - 可以逐个组件迁移到新的 `fieldNames` 配置
3. **测试验证** - 迁移后测试确保功能正常

### 迁移示例

```javascript
// 旧方式
<CupAutoComplete typevalue="4" options={labelOptions} />

// 新方式
<CupAutoComplete 
  fieldNames={{ label: 'label', value: 'id', key: 'id' }}
  options={labelOptions} 
/>
```

现在你的 `CupAutoComplete` 组件可以灵活处理各种数据结构了！🎉
