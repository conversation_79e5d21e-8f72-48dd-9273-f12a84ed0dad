import React, { useState } from "react";
import LayoutList from "@/components/ListLayout.jsx";
import { debounce } from "lodash-es";
import { useTranslation } from "react-i18next";
import TableList from "./component/TableList";
import { getPage } from "@s/screens.js";
function index() {
  const { t } = useTranslation();

  const [data, setData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isRefetching, setIsRefetching] = useState(false);
  const [isError, setIsError] = useState(false);
  const [rowCount, setRowCount] = useState(0);

  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 5,
  });

  const buildParams = () => {
    const params = {
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
    };

    return params;
  };

  const getTableData = useCallback(
    debounce(() => {
      if (!data.length) {
        setIsLoading(true);
      } else {
        setIsRefetching(true);
      }

      getPage(buildParams())
        .then((res) => {
          // 设置数据
          setData(res.data.data);
          // 设置总记录数
          setRowCount(res.data.total);
          setIsLoading(false);
          setIsRefetching(false);
        })
        .catch((err) => {
          setIsError(true);
          setIsLoading(false);
          setIsRefetching(false);
        });
    }, 50),
    [pagination.pageIndex, pagination.pageSize]
  );

  useEffect(() => {
    getTableData();
  }, [pagination]);

  const rederTable = () => {
    return (
      <TableList
        data={data}
        isLoading={isLoading}
        isRefetching={isRefetching}
        isError={isError}
        rowCount={rowCount}
        pagination={pagination}
        setPagination={setPagination}
        getTableData={getTableData}></TableList>
    );
  };

  return (
    <React.Fragment>
      <LayoutList title={t("menu.screens")} content={rederTable()}></LayoutList>
    </React.Fragment>
  );
}

export default index;
