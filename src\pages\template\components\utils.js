
import { toast } from "react-toastify";
import { getPages, deletes } from "@/services/TemplateService.js";
export const handleDelete = (id, setConfirm) => {
  let ids = [];
  ids.push(id);
  deletes({ ids: ids }).then((response) => {
    toast.success(response.message);

    setConfirm(false);
  });
};

export const getTemplateType = (value) => {
  const option = templateTypeOptions.find((option) => option.id == value);
  return option ? option.value : "";
};

export const getPicPreview = (key) => {
  getDownloadUrl(key.row.templateObjectKey).then((res) => {
    if (res?.data?.data && res?.data?.code === "LVLI0000") {
      setHtmlContent("");
      let templateObj = [];
      records.map((e) => {
        let templateData = e;
        if (e.templateObjectKey === res.data.data.objectKey) {
          templateData.preSignedUrl = res.data.data.downloadUrl;
        }
        templateObj.push(templateData);
      });
      setRecords(templateObj);
      popUp(res.data.data.downloadUrl);
    }
  });
};

export const loadData = (
  filters,
  setRecords,
  setTotalRecords,
  setDownloadUrls, setLoading
) => {
  setLoading(true)
  getPages(filters).then((res) => {
    setRecords(res.data?.data);
    setTotalRecords(res.data?.total);
    setDownloadUrls(res?.data?.data?.downloadUrlData);
    setLoading(false)
  });
};

