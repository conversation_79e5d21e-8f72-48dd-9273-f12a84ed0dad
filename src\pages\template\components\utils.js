

export const getTemplateType = (value) => {
  const option = templateTypeOptions.find((option) => option.id == value);
  return option ? option.value : "";
};

export const getPicPreview = (key) => {
  getDownloadUrl(key.row.templateObjectKey).then((res) => {
    if (res?.data?.data && res?.data?.code === "LVLI0000") {
      setHtmlContent("");
      let templateObj = [];
      records.map((e) => {
        let templateData = e;
        if (e.templateObjectKey === res.data.data.objectKey) {
          templateData.preSignedUrl = res.data.data.downloadUrl;
        }
        templateObj.push(templateData);
      });
      setRecords(templateObj);
      popUp(res.data.data.downloadUrl);
    }
  });
};

