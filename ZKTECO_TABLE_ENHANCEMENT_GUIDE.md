# ZktecoTable 增强功能使用指南

## 问题解决

### ✅ 1. 横向滚动条问题已解决

**问题**: 表格列过多时，底部没有出现横向滚动条

**解决方案**: 
- 优化了 `muiTableContainerProps` 配置
- 添加了自定义滚动条样式
- 确保滚动条始终可见

```javascript
muiTableContainerProps={{
  sx: { 
    maxHeight: "100%", 
    overflowX: "auto",
    overflowY: "auto",
    minWidth: "100%",
    // 自定义滚动条样式
    "&::-webkit-scrollbar": {
      height: "8px",
      width: "8px",
    },
    "&::-webkit-scrollbar-track": {
      backgroundColor: "#f1f1f1",
      borderRadius: "4px",
    },
    "&::-webkit-scrollbar-thumb": {
      backgroundColor: "#c1c1c1",
      borderRadius: "4px",
      "&:hover": {
        backgroundColor: "#a8a8a8",
      },
    },
  },
}}
```

### ✅ 2. 自定义图标功能已实现

**新增功能**:
- 表格右上角支持 4 个自定义图标
- 操作列支持 3 个自定义图标
- 完全可配置的图标、颜色、回调函数

## 使用方法

### 1. 表格右上角自定义图标

```javascript
// 定义自定义顶部操作
const customTopActions = [
  {
    icon: <FilterListIcon />,
    title: "筛选",
    tooltip: "筛选数据",
    onClick: () => {
      console.log("筛选操作");
      toast.info("筛选功能");
    },
    color: "#1976d2",
  },
  {
    icon: <SearchIcon />,
    title: "搜索", 
    tooltip: "搜索数据",
    onClick: () => {
      console.log("搜索操作");
      toast.info("搜索功能");
    },
    color: "#2e7d32",
  },
  {
    icon: <SettingsIcon />,
    title: "设置",
    tooltip: "表格设置", 
    onClick: () => {
      console.log("设置操作");
      toast.info("设置功能");
    },
    color: "#ed6c02",
  },
  {
    icon: <ExportIcon />,
    title: "导出",
    tooltip: "导出数据",
    onClick: () => {
      console.log("导出操作");
      toast.info("导出功能");
    },
    color: "#9c27b0",
  },
];

// 在 ZktecoTable 中使用
<ZktecoTable
  topActions={{
    showAdd: false,
    customActions: customTopActions, // 传入自定义操作
  }}
/>
```

### 2. 操作列自定义图标

```javascript
// 定义自定义行操作
const renderCustomRowActions = useCallback(({ row }) => {
  return (
    <Grid container spacing={1} sx={{ display: "flex", flexDirection: "row" }}>
      {/* 原有的绑定图标 */}
      {(row.original.isFree == 0 || row.original.subStatus == 3) && (
        <Grid item>
          <Tooltip title={t("screen.bindData")} arrow placement="bottom">
            <BindIcon
              style={{
                cursor: "pointer",
                height: "17px",
                width: "20px",
              }}
              onClick={() => handleDataBind(row.original.id, data, navigate)}
            />
          </Tooltip>
        </Grid>
      )}
      
      {/* 新增的自定义操作图标 */}
      <Grid item>
        <Tooltip title="查看详情" arrow placement="bottom">
          <VisibilityIcon
            sx={{
              cursor: "pointer",
              color: "#1976d2",
              fontSize: "18px",
              "&:hover": { color: "#1565c0" },
            }}
            onClick={() => {
              console.log("查看详情", row.original);
              toast.info(`查看 ${row.original.screenName} 详情`);
            }}
          />
        </Tooltip>
      </Grid>
      
      <Grid item>
        <Tooltip title="快速编辑" arrow placement="bottom">
          <EditIcon
            sx={{
              cursor: "pointer",
              color: "#2e7d32", 
              fontSize: "18px",
              "&:hover": { color: "#1b5e20" },
            }}
            onClick={() => {
              console.log("快速编辑", row.original);
              toast.info(`编辑 ${row.original.screenName}`);
            }}
          />
        </Tooltip>
      </Grid>
      
      <Grid item>
        <Tooltip title="删除" arrow placement="bottom">
          <DeleteIcon
            sx={{
              cursor: "pointer",
              color: "#d32f2f",
              fontSize: "18px", 
              "&:hover": { color: "#c62828" },
            }}
            onClick={() => {
              if (window.confirm(`确定要删除 ${row.original.screenName} 吗？`)) {
                toast.success("删除成功");
              }
            }}
          />
        </Tooltip>
      </Grid>
    </Grid>
  );
}, [t, data, navigate]);

// 在 ZktecoTable 中使用
<ZktecoTable
  renderRowActions={renderCustomRowActions}
/>
```

## 配置选项

### 自定义顶部操作配置

| 属性 | 类型 | 必填 | 描述 |
|------|------|------|------|
| `icon` | ReactNode | ✅ | 图标组件 |
| `title` | String | ✅ | 操作标题 |
| `tooltip` | String | ❌ | 悬停提示文本 |
| `onClick` | Function | ✅ | 点击回调函数 |
| `color` | String | ❌ | 图标颜色 |
| `backgroundColor` | String | ❌ | 背景颜色 |
| `hoverColor` | String | ❌ | 悬停背景颜色 |
| `disabled` | Boolean | ❌ | 是否禁用 |
| `sx` | Object | ❌ | 自定义样式 |

### 自定义行操作配置

行操作通过 `renderRowActions` 函数完全自定义，你可以：

- 使用任意 Material-UI 图标
- 自定义颜色和样式
- 添加条件显示逻辑
- 集成 Tooltip 提示
- 处理点击事件

## 样式定制

### 滚动条样式

滚动条样式已经预设，你也可以进一步自定义：

```javascript
"&::-webkit-scrollbar": {
  height: "8px",        // 横向滚动条高度
  width: "8px",         // 纵向滚动条宽度
},
"&::-webkit-scrollbar-track": {
  backgroundColor: "#f1f1f1",  // 滚动条轨道颜色
  borderRadius: "4px",
},
"&::-webkit-scrollbar-thumb": {
  backgroundColor: "#c1c1c1",  // 滚动条滑块颜色
  borderRadius: "4px",
  "&:hover": {
    backgroundColor: "#a8a8a8", // 悬停时滑块颜色
  },
},
```

### 图标样式

```javascript
// 统一的图标样式
const iconStyle = {
  cursor: "pointer",
  fontSize: "18px",
  transition: "color 0.2s ease",
  "&:hover": {
    transform: "scale(1.1)",
  },
};

// 不同状态的颜色
const colors = {
  primary: "#1976d2",
  success: "#2e7d32", 
  warning: "#ed6c02",
  error: "#d32f2f",
  info: "#0288d1",
};
```

## 最佳实践

### 1. 图标选择
- 使用语义化的图标
- 保持图标风格一致
- 合理使用颜色区分功能

### 2. 交互反馈
- 添加 Tooltip 提示
- 使用悬停效果
- 提供操作确认

### 3. 性能优化
- 使用 `useCallback` 缓存回调函数
- 使用 `useMemo` 缓存配置对象
- 避免在渲染函数中创建新对象

### 4. 响应式设计
- 考虑移动端显示
- 合理控制图标数量
- 使用适当的间距

现在你的 ZktecoTable 组件具备了完整的横向滚动和自定义图标功能！🎉
