import React, { useState } from "react";
import RightViewLayout from "@c/RighViewLayout";
import DataTable from "@c/DataTable";
import BindIcon from "@a/images/bind_icon.svg?react";
import RefreshIcon from "@/assets/Icons/Refresh Icon.svg?react";
import ImportIcons from "@/assets/Icons/batch import Icon.svg?react";
import DownloadIcons from "@/assets/Icons/Export Icon.svg?react";
import BatchUpdateScreen from "./component/BatchUpdateScreen";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import EditIcon from "@mui/icons-material/Edit";
import {
  toolbarProps,
  loadData,
  handleBinds,
  handleBatchUpdate,
  removeDuplicate,
  handleSelection,
} from "./component/utils";
import { getColums, searchScreenProps } from "./component/Colums";
import PreViewTemplate from "./component/PreViewTemplate";
import { useStatePermission } from "@/hooks/user";
import AuthButton from "@/components/AuthButton.jsx";
import MoreOption from "@/components/DataTableMoreOption";
import ImportBIndingSheet from "./component/ImportBIndingSheet";

function index() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const permission = JSON.parse(
    sessionStorage.getItem("USER_INFO")
  )?.permissions;
  const [records, setRecords] = useState([]);
  const [selectedRows, setSelectedRows] = useState([]);
  const [totalRecords, setTotalRecords] = React.useState(0);
  const [preViewOpen, setPreViewOpen] = useState(false);
  const [batchUpdateOpen, setBatchUpdateOpen] = useState(false);
  const [preViewItem, setPreViewItem] = useState(null);
  const [searchValue, setSearchValue] = useState("");
  const [allSelectedRows, setAllSelectedRows] = useState([]);
  const [importOpen, setImportOpen] = useState(false);
  const [filters, setFilters] = useState({
    page: 1,
    pageSize: 5,
  });

  useEffect(() => {
    loadData(filters, setRecords, setTotalRecords, setLoading);
  }, [filters]);

  const handlePageChange = (e) => {
    setAllSelectedRows(removeDuplicate([...allSelectedRows, ...selectedRows]));
    setFilters({ ...filters, page: e + 1, id: searchValue });
    setPage(e + 1);
  };

  const handlePageSize = (e) => {
    setAllSelectedRows(removeDuplicate([...allSelectedRows, ...selectedRows]));
    setFilters({
      ...filters,
      page: filters.page,
      pageSize: e,
      id: searchValue,
    });
    setRowsPerPage(e);
  };

  return (
    <React.Fragment>
      <RightViewLayout
        title={t("menu.screens")}
        navigateBack={false}
        toolbarProps={toolbarProps}
        globalFilterProps={false}
        searchProps={searchScreenProps(
          searchValue,
          setSearchValue,
          t,
          filters,
          setFilters
        )}
      >
        <Grid
          container
          direction={"row"}
          style={{ display: "flex", justifyContent: "flex-end" }}
          p={1}
        >
          <AuthButton button="nt:nutag:screen:download">
            <Tooltip title={t("screen.import_binding_sheet")}>
              <Grid
                id="Product-button-06"
                style={{
                  height: "35px",
                  width: "35px",
                  cursor: "pointer",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  borderRadius: "4px",
                  background: "#fff",
                  marginLeft: "15px",
                }}
              >
                <DownloadIcons onClick={() => setImportOpen(true)} />
              </Grid>
            </Tooltip>
          </AuthButton>
          {/* </Tooltip> */}
          {/* </AuthButton> */}

          {/* <AuthButton button="nt:nutag:screen:batch_update"> */}
          <Tooltip title={t("screen.batchUpdateScreen")}>
            <Grid
              id="Product-button-06"
              style={{
                height: "35px",
                width: "35px",
                cursor: "pointer",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                borderRadius: "4px",
                background: "#fff",
                marginLeft: "15px",
              }}>
              <MoreOption
                data={[
                  { id: 1, label: "Export the selected" },
                  { id: 2, label: "Export All" },
                ]}
                handleSelect={(option) => {
                  console.log(option);
                }}>
                <ImportIcons />
              </MoreOption>
            </Grid>{" "}
          </Tooltip>
          {/* </AuthButton> */}

          <AuthButton button="nt:nutag:screen:batch_send">
            <Tooltip title={t("screen.bindData")}>
              <Grid
                id="Product-button-06"
                style={{
                  height: "35px",
                  width: "35px",
                  cursor: "pointer",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  borderRadius: "4px",
                  background: "#fff",
                  marginLeft: "15px",
                }}
              >
                <BindIcon
                  sx={{ color: "white" }}
                  onClick={() => handleBinds(allSelectedRows, navigate, t)}
                />
              </Grid>
            </Tooltip>
          </AuthButton>

          <AuthButton button="nt:nutag:screen:batch_update">
            <Tooltip title={t("screen.batchUpdateScreen")}>
              <Grid
                id="Product-button-06"
                style={{
                  height: "35px",
                  width: "35px",
                  cursor: "pointer",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  borderRadius: "4px",
                  background: "#fff",
                  marginLeft: "15px",
                }}
              >
                <EditIcon
                  style={{
                    opacity: "0.4",
                  }}
                  onClick={() =>
                    handleBatchUpdate(setBatchUpdateOpen, allSelectedRows)
                  }
                />
              </Grid>
            </Tooltip>
          </AuthButton>

          <Tooltip title={t("common.refresh")}>
            <Grid
              id="Product-button-06"
              style={{
                height: "35px",
                width: "35px",
                cursor: "pointer",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                borderRadius: "4px",
                background: "#fff",
                marginLeft: "15px",
                marginRight: 12,
              }}
            >
              <RefreshIcon
                onClick={() => {
                  setSearchValue("");
                  setSelectedRows([]);
                  setAllSelectedRows([]);
                  setFilters({ ...filters });
                }}
              />
            </Grid>
          </Tooltip>
        </Grid>

        <DataTable
          columns={getColums(
            t,
            setPreViewOpen,
            setPreViewItem,
            navigate,
            records
          )}
          rows={records}
          getRowId={(screen) => screen.id}
          page={filters.page - 1}
          totalRecords={totalRecords}
          rowsPerPage={filters.pageSize}
          onSelection={(id) =>
            handleSelection(
              id,
              records,
              setAllSelectedRows,
              setSelectedRows,
              allSelectedRows,
              t
            )
          }
          rowSelectionModel={allSelectedRows?.map((item) => {
            return Object.keys(item)[0];
          })}
          loading={loading}
          onPageChange={(pn) => handlePageChange(pn)}
          onPageSizeChange={(ps) => handlePageSize(ps)}
          checkboxSelection={true}
          disableRowSelectionOnClick={true}
        />

        {/* <PreViewTemplate
          preViewOpen={preViewOpen}
          setPreViewOpen={setPreViewOpen}
          setPreViewItem={setPreViewItem}
          preViewItem={preViewItem}></PreViewTemplate> */}

        <BatchUpdateScreen
          open={batchUpdateOpen}
          setOpen={setBatchUpdateOpen}
          screenIds={allSelectedRows.map((x) => Object.keys(x)[0])}
          loadData={loadData}
        ></BatchUpdateScreen>

        <ImportBIndingSheet
          open={importOpen}
          onClose={() => setImportOpen(false)}
        ></ImportBIndingSheet>
      </RightViewLayout>
    </React.Fragment>
  );
}

export default index;
