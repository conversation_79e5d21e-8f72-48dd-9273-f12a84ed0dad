import request from "@/util/request";

/**
 *   获取路由菜单接口
 * <AUTHOR>
 * @date 2025-02-18 14:35
 */

export const getUserMenus = (params) => {
  return request({
    url: `${import.meta.env.VITE_GLOBAL}/auth/resource/menu`,
    method: "GET",
    params: params,
  });
};

/**
 *  获取门店下拉列表接口
 * <AUTHOR>
 * @date 2025-02-18 14:35
 */

export const getOutletList = (params) => {
  return request({
    url: `${import.meta.env.VITE_GLOBAL}/outlet/query/list`,
    method: "GET",
    params: params,
  });
};

/**
 *  获取门店分页列表接口
 * <AUTHOR>
 * @date 2025-02-18 14:35
 */

export const getOutletPageList = (params) => {
  return request({
    url: `${import.meta.env.VITE_GLOBAL}/outlet/query/page`,
    method: "GET",
    params: params,
  });
};

/**
 *
 * 通过productTypeId查询productTypeValue
 *
 * @argument属性值  productTypeId
 *
 * <AUTHOR>
 * @date 2025-02-18 14:35
 */

export const getProductValue = (id) => {
  return request({
    url: `${import.meta.env.VITE_GLOBAL}/product_type/product_type_value/${id}`,
    method: "GET",
  });
};

/**
 *
 * 通过productTypeValue查询产品
 *
 * @argument属性值  productTypeValue
 * <AUTHOR>
 * @date 2025-02-18 14:35
 */

export const getProduct = (params) => {
  return request({
    url: `${import.meta.env.VITE_GLOBAL}/product/type_id_value`,
    method: "GET",
    params: params,
  });
};

/**
 *
 * 根据产品和选中的促销类型查询所有分辨率的模板;
 *
 * @argument属性值  productId  promotionType
 * <AUTHOR>
 * @date 2025-02-18 14:35
 */

export const getProductLabel = (params) => {
  return request({
    url: `${import.meta.env.VITE_GLOBAL}/product_type`,
    method: "GET",
    params: params,
  });
};

/**
 *
 * 根据产品和选中的促销类型查询所有分辨率的模板;
 *
 * @argument属性值  productId  promotionType
 * <AUTHOR>
 * @date 2025-02-18 14:35
 */

export const getGateWay = (params) => {
  return request({
    url: `${import.meta.env.VITE_GLOBAL}/device/query/page`,
    method: "GET",
    params: params,
  });
};

// 按id查找产品;
export const getProductById = (id) => {
  return request({
    url: `${import.meta.env.VITE_GLOBAL}/product/${id}`,
    method: "GET",
  });
};

//  获取产品下拉列表数据
export const getAllProducts = (params) => {
  return request({
    url: `${import.meta.env.VITE_GLOBAL}/product`,
    method: "GET",
    params: params,
  });
};

// 上传图片接口
export const uploadImage = (params) => {
  return request({
    url: `${import.meta.env.VITE_GLOBAL}/upload/upload_s3`,
    method: "POST",
    data: params,
    headers: {
      "Content-Type": "multipart/form-data", // 手动设置 Content-Type 为 multipart/form-data
    },
  });
};


/**
 *  获取按钮级权限
 */

export const getAuthButton = (params) => {
  return request({
    url: `${import.meta.env.VITE_GLOBAL}/auth/application/${params}`,
    method: "POST",

  });
};


/**
 *  通过商品 ID 和 零售商ID 查询商品类型
 */

export const getProductType = (productId) => {
  return request({
    url: `${import.meta.env.VITE_GLOBAL}/product_type/product_type/${productId}`,
    method: "GET",

  });
};
