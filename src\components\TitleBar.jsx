import { Box, Tooltip, Typography, Grid } from "@mui/material";
import React from "react";
import { useNavigate } from "react-router-dom";
import KeyBoardLeftArrowIcon from "@/assets/images/KeyboardArrowLeftIcon.svg?react";
import { useTranslation } from "react-i18next";
const TitleBar = (props) => {
  let navigate = useNavigate();
  const { t } = useTranslation();
  return (
    <>
      <Box
        onClick={() =>
          props?.navigateBack == "-1"
            ? navigate(-1)
            : navigate(props?.navigateBack)
        }>
        {props.navigateBack && (
          <Tooltip title={t("common.back")}>
            <Grid
              container
              sx={{
                cursor: "pointer",
                display: "flex",
                alignItems: "center",
                gap: "15px",
              }}>
              <KeyBoardLeftArrowIcon width={18} height={18} fontSize="small" />
              <Typography variant="h4">{props.title}</Typography>
            </Grid>
          </Tooltip>
        )}

        {props.children}
      </Box>
    </>
  );
};
export default TitleBar;
