import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate, useLocation } from "react-router-dom";
import RightViewLayout from "@c/RighViewLayout";
import { list } from "@s/resolution.js";
import { getDetail, edit, add } from "@s/TemplateService.js";
import { toast } from "react-toastify";
import ZkFormik from "@/components/Config/CmpFormik";
import { useFormik } from "formik";
import { getFormConfig } from "./FormConfig";
import { createValidation } from "@c/Config/validationUtils.js";
function AddTemplate() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { state } = useLocation();
  const [loading, setLoading] = React.useState(false);
  const [formConfig, setFormConfig] = useState([]);
  const [resolution, setResolution] = useState([]);
  const [modelList, setModelList] = useState([]);

  const [detail, setDetail] = useState({});

  useEffect(() => {
    list().then((res) => {
      setResolution(res?.data?.valueList);

      const modelList = [];
      Object.values(res?.data?.modelList).forEach((arr) => {
        arr.forEach((item) => {
          if (item.model) {
            modelList.push(item.model);
          }
        });
      });
      const uniqueResolutions = Array.from(new Set(modelList));
      setModelList(uniqueResolutions);
    });

    if (state?.type == "editor") {
      getDetail(state?.id).then((res) => {
        setDetail(res?.data);
      });
    }
  }, []);

  useEffect(() => {
    const formConfig = getFormConfig(t, resolution, modelList);
    setFormConfig(formConfig);
  }, [modelList, resolution]);

  let initialValues = {};
  if (state?.type !== "editor") {
    initialValues = {
      name: "",
      orientation: "",
      resolution: "",
      model: "",
      promotionType: "",
    };
  } else {
    initialValues = {
      id: state.id,
      name: detail?.name,
      orientation: detail?.orientation,
      resolution: detail?.resolution,
      model: detail?.model,
      promotionType: detail?.promotionType,
    };
  }

  const formik = useFormik({
    initialValues: initialValues,
    validationSchema: createValidation(formConfig),
    enableReinitialize: true, // 允许重新初始化
    onSubmit: (values) => {
      handlerNext(values);
      if (state?.type == "editor") {
        setLoading(true);
        const params = {
          ...values,
          orientation: values.orientation.id
            ? values.orientation.id
            : values.orientation,
          promotionType: values.promotionType.id
            ? values.promotionType.id
            : values.promotionType,
        };
        edit(state?.id, params).then((res) => {
          if (res?.code == "00000000") {
            setLoading(false);
            navigate("/picture-library");
            toast.success(res.message);
          } else {
            toast.error(res.message);
            setLoading(false);
          }
        });
      }
    },
  });

  const handlerNext = (payload) => {
    if (state?.type == "editor") {
      const name = payload.name;
      const resolution = payload.resolution;
      const orientation = payload.orientation?.id;
      const model = payload.model;
      const type = payload.promotionType?.id;
      const templateObjectKey = payload.templateObjectKey;
      const templateImage = image;
      const id = payload.id;
      const imageObjectKey = payload.imageDownloadObjectKey;

      const templateJson = payload.templateJson;

      navigate("/editor", {
        state: {
          name,
          resolution,
          orientation,
          model,
          type,
          templateObjectKey,
          templateImage,
          id,
          imageObjectKey,
          templateJson,
          isEditor: true,
        },
      });
    } else {
      const name = payload.name;
      const resolution = payload.resolution;
      const orientation = payload.orientation;
      const model = payload.model;
      const type = payload.promotionType;

      navigate("/editor", {
        state: {
          name,
          resolution,
          orientation,
          model,
          type,
          isEditor: false,
        },
      });
    }
  };

  return (
    <React.Fragment>
      <RightViewLayout
        title={
          state?.type == "editor"
            ? t("template.edit_template")
            : t("template.add_template")
        }
        navigateBack={"/template"}
        isShowSaveButton={false}
        loading={loading}>
        <Grid mt={4}>
          <ZkFormik sx={6} formik={formik} formConfig={formConfig}></ZkFormik>
        </Grid>

        <Grid container md={12} xs={12}>
          <Box
            display={"flex"}
            flexDirection={"row-reverse"}
            style={{ marginTop: "30px", width: "100%" }}>
            <Box item>
              <Button
                id="addtempnext"
                variant="contained"
                size="large"
                className="text-transform-none"
                onClick={() => handlerNext(formik.values)}
                style={{
                  size: "medium",
                  background:
                    "transparent linear-gradient(270deg, #1487CA 0%, #78BC27 100%) 0% 0% no-repeat padding-box",
                  borderRadius: "8px",
                  opacity: 1,
                }}>
                {t("common.next")}
              </Button>
            </Box>
            {state?.type == "editor" && (
              <Box item mr={2}>
                <Button
                  id="addtempnext"
                  variant="outlined"
                  size="large"
                  className="text-transform-none"
                  onClick={formik.handleSubmit}>
                  {t("common.save")}
                </Button>
              </Box>
            )}

            <Box item mr={2}>
              <Button
                id="addtempcan"
                className="text-transform-none"
                variant="outlined"
                onClick={() => navigate("/template")}
                size="large">
                {t("common.cancel")}
              </Button>
            </Box>
          </Box>
        </Grid>
      </RightViewLayout>
    </React.Fragment>
  );
}

export default AddTemplate;
