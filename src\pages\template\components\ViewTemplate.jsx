import React from "react";
import RightViewLayout from "@c/RighViewLayout";
import { useTranslation } from "react-i18next";
import { useLocation } from "react-router-dom";
import { getDetail } from "@s/TemplateService";
import ViewBox from "@c/ViewBox";

function ViewTemplate() {
  const { t } = useTranslation();
  const { state } = useLocation();
  const [payload, setPayload] = useState({
    name: "",
    resolution: "",
    orientation: "",
  });

  useEffect(() => {
    getDetail(state?.id).then((res) => {
      setPayload({
        ...payload,
        ...res.data,
      });
    });
  }, []);

  return (
    <React.Fragment>
      <RightViewLayout
        id="viewtempback"
        navigateBack={"-1"}
        title={t("template.view_template")}>
        <Grid container flexDirection={"column"} gap={4} m={4}>
          <ViewBox title={t("template.name")} content={payload.name} />
          <ViewBox
            title={t("template.resolution")}
            content={payload.resolution}
          />
          <ViewBox
            title={t("common.type")}
            content={
              <span>
                {payload.templateType == "0"
                  ? t("common.system_default")
                  : t("common.personal_template")}
              </span>
            }
          />

          <ViewBox title={t("template.screen_model")} content={payload.model} />

          <ViewBox
            title={t("template.screen_direction")}
            content={
              <span>
                {payload.orientation == "0"
                  ? t("dictionary.vertical")
                  : t("dictionary.horizontal")}
              </span>
            }
          />
        </Grid>
      </RightViewLayout>
    </React.Fragment>
  );
}

export default ViewTemplate;
