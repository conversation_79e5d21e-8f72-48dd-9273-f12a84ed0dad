export default {
  login: {
    wrong_password: "密码错误",
    enter_email: "请输入您的邮箱",
  },

  about: {
    version: "版本",
    about_system: "本系统建议使用浏览器",
    display_resolution: "显示分辨率",
    package_details: "套餐详情",
    remaining_valid_days: "剩余有效天数：",
    remaining_device_count: "剩余设备数量：",
    no_used: "使用数量：",
    total_no_limit: "总限制数",
  },

  company: {
    company_profile: "公司简介",
    name: "名称 :",
    code: "编码 :",
    phone: "电话 :",
    email: "电子邮箱 :",
    first_name: "名字 :",
    last_name: "姓氏 :",
    mobile_number: "手机号码 ：",
    city: "城市 :",
    country: "国家 :",
    address: "地址 :",
    mobile: "手机 :",
    state: "省份 :",
  },

  retail_client: {
    add_retail_client: "添加零售商",
    edit_retail_client: "编辑零售商",
    view_retail_client: "查看零售商",
    retail_client_name: "零售商名称",
    retail_client_code: "零售商编码",
    retail_admin_email: "零售商管理员电子邮箱",
    retail_client_first_name: "零售商管理员名字",
    retail_client_last_name: "零售商管理员姓氏",
    number_of_devices: "设备数量",
    subscription_start_date: "订阅开始日期",
    subscription_expire_date: "订阅到期日期",
    select_one_retail: "选择想要运维的零售商",
  },

  template: {
    view_template: "查看模板",
    add_template: "添加模板",
    edit_template: "编辑模板",
    template_editor: "模板编辑器",
    name: "模板名称",
    resolution: "分辨率",
    type: "模板类型",
    screen_direction: "屏幕方向",
    preview: "模板预览",
    screen_model: "屏幕型号",
    is_bind_device: "是否绑定基站",
  },

  editor: {
    product_element: "产品元素",
    price_pule: "价格规则",
    product_price: "产品价格",
    product_label: "产品标签",
    quantity: "数量",
    category1: "类别1",
    category2: "类别2",
    currency: "币别",
    origin: "产地",
    manufacturer: "制造商",
    discount_price: "折扣价格",
    company_logo: "公司标志",
    basic_tags: "基本标签",
    image_library: "图片库",
    change_rule: "变更规则",
    free_text: "自由文本",
    add_label: "添加标签",
    add_shape: "添加形状",
    delete_element: "删除元素",
    rounded_corners: "圆角：",
    content_example: "内容示例 :",
    bgcolor: "背景颜色：",
    show_border: "显示边框：",
    show_scale: "显示比例：",
    show_value: "显示值：",
    font_color: "字体颜色：",
    font_size: "字体大小：",
    font_style: "字体样式",
    font_weight: "字体粗细：",
    font: "字体：",
    spacing: "间距：",
    rotation_angle: "旋转角度：",
    width: "宽度：",
    height: "高度：",
    opacity: "透明度：",
    line_color: "线条颜色：",
    foreground_color: "前景色：",
    level: "等级 :",
    color: "颜色",
    shape: "形状",
    line_height: "行高 :",
    content: "内容",
    qrcode: "二维码",
    barcode: "条形码",
    image: "图片",
    textBox: "文本",
  },

  openAPI: {
    open_api_client: "开放API客户端",
    add_open_api: "添加开放API客户端",
    secret_key: "密钥",
  },

  picture_library: {
    picture_library: "图片库",
    add_picture: "添加图片",
    edit_picture: "编辑图片",
    name: "图片名称",
    id: "图片编码",
    preview: "图片预览",
    type: "图片类型",
    size: "图片大小",
    progressing: "图片预处理",
    general_user: "通用",
    company_logo: "公司标志",
    original_image: "原始图片",
    dithering_image: "抖动图片",
    please_picture_type: "请选择图片类型",
    please_select: "请选择"
  },

  dashboard: {
    welcome_back: "欢迎回来！",
    login_for_next: "您的订阅已过期，您可以登录进行下一步",
    days_only: " 仅限天",
    subscription_expires: "您的订阅将于",
    title: "NuTag看板",
    from_date: "开始日期",
    period: "周期",
    registered_gateway: "注册网关",
    registered_ESL: "注册价签",
    gateway_online_status: "网关在线状态",
    last_refreshed: "上次刷新",
    refresh_success_rate: "刷新成功率",
    total_refreshes: "总刷新次数",
    total_number: "指定时间段内的价签更新总数",
    refreshes: "刷新",
    devices_distribution: "设备分布",
    esl_total_stores: "拥有电子货架标签(ESL) 的门店数量VS总门店数量",
    esl_stores: "已安装电子货架标签(ESL)的门店数量",
    total_stores: "总门店数",
    refresh_events: "刷新事件",
    time_in_hours: "时间（小时）",
    refresh_time: "刷新次数",
    success_rate: "成功率",
    day: "天",
    store: "门店",
    all_stores: "所有门店",
    apply_filters: "查询",
    period_from_date: "起始日期",
    stores_installed: "已安装门店",
    period_to_date: "截至目前",
    select_store: "请选择门店",
  },

  authorization: {
    title: "权限组",
    add: "添加权限组",
    edit: "编辑权限组",
    view: "查看权限组",
    name: "权限组名称",
    list: "权限组列表",
  },

  product: {
    title: "产品信息",
    add_product_label: "添加产品标签",
    sku: "产品最小存货单位",
    category_level1: "类别1",
    category_level2: "类别2",
    category_level3: "类别3",
    currency_symbol: "货币符号",
    brand_name: "品牌名称",
    origin_of_product: "产品产地",
    product: "产品",
    add_product: "添加产品",
    view_product: "查看产品",
    edit_product: "编辑产品",
    product_picture: "产品图片",
    product_price: "产品价格",
    discount_price: "折扣价格",
    serial_number: "序号",
    number_one: "1",
    coca_cola: "可口可乐",
    drink: "饮料",
    cold_drink: "冷饮",
    number_two: "2",
    model: "型号 :",
    inc_screen_iD: "Inc 屏幕 ID :",
    position_no: "位置编号 :",
    resolution: "分辨率 :",
  },

  outlet: {
    title: "门店",
    add_outlet: "添加门店",
    edit_outlet: "编辑门店",
    view_outlet: "查看门店",
    name: "门店名称",
    code: "门店编码",
    selected_outlet: "已经选中的门店",
  },

  user: {
    name: "用户管理",
    person_list: "用户列表",
    create_person: "创建用户",
    edit_person: "编辑用户",
    view_person: "查看用户",
    person_name: "用户",
    person_Id: "用户",
    user_profile: "用户",
    person_avatar: "用户头像",
  },

  gateway: {
    gateway: "网关",
    viewDeviceDetails: "查看设备详细信息",
    addDeviceToOutlet: "将设备添加到门店",
    bindDeviceToOutlet: "绑定设备到门店",
    gatewaySerialNumber: "网关序列号",
    gatewayName: "网关名称",
    gatewayIPAddress: "网关IP地址",
    gatewayFirmware: "网关固件",
    gatewayRunningStatus: "网关运行状态",
    gatewayUpdateTime: "网关更新时间",
    outlet: "门店",
    outletTimeZone: "门店时区",
    versionNumber: "版本号r",
    gatewayType: "网关类型",
    gatewayModel: "网关型号",
    outletCode: "门店编码",
    logTime: "日志时间",
    logType: "日志类型",
    logCode: "日志编码",
    enable: "启用",
    deviceName: "设备名称",
    deviceModel: "设备型号",
    serialNumber: "序列号",
    deviceType: "设备类型",
    outletId: "门店编号",
    clientName: "客户名称",
    deviceLog: "设备日志",
    view_gateway_details: "查看网关详情",
    select_version: "请选择一个版本",
  },

  screen: {
    edit_screen: "编辑屏幕",
    bind_to_screen: "绑定数据到屏幕",
    screenId: "屏幕编号",
    screen_resolution: "屏幕分辨率",
    positionNo: "位置号",
    screenName: "屏幕名称",
    powerStatus: "电源状态",
    gateway: "网关",
    outlet: "门店",
    bindToProduct: "绑定到产品",
    templatePreview: "模板预览",

    screenStatus: "屏幕状态",
    screenUpdateTime: "屏幕更新时间",
    gatewaySerialNumber: "网关序列号",
    gatewayName: "网关名称",
    deviceModel: "设备型号",
    deviceType: "设备类型",
    gatewayRunningStatus: "网关运行状态",
    unbindDevice: "接触设备绑定",

    bindData: "绑定数据",
    normal: "正常",
    lowPower: "低电量",
    total: "总计",
    unbound_templates: "未绑定模板",
    batchUpdateScreen: "批量编辑屏幕",
    same_screen: "请选择相同分辨率的屏幕",
    is_bindDevice: "请选择是否绑定设备",
    select_online_screen: "请选择在线屏幕",
    select_one_screen: "请选择屏幕",
    same_screen: "请选择相同分辨率的屏幕",
    import_binding_sheet: "导入绑定工作表",
    click_download_sample_file: "点击这里下载示例文件",
    download_template: "下载模板",
    total_colon: "总共:",
    processed_colon: "处理:",
    remaining_colon: "剩余:",
    success_colon: "成功:",
    failed_colon: "失败:",
    status_colon: "状态:",
    choose_file_import: "选择一个文件去导入",
    import_colon: "导入:",
    import_desc: "导入只绑定关系.",
    refresh_colon: "刷新:",
    refresh_desc: "导入绑定关系并且刷新。",
    import_cancelled_by_user: "用户取消导入",
    importing: "导入中...",
    import_and_refresh: "导入和刷新",
    status_pending: "待处理",
    status_running: "导入中",
    status_success: "成功",
    status_failed: "失败",
    import_completed_with_success_and_failures:
      "导入完成，其中成功{{success}}条，失败{{failed}}条",
    import_completed_successfully: "导入成功！处理了{{success}}条数据。",
    import_failed: "导入失败",
    failed_fetch_import_progress: "不能获取导入进度",
  },

  events: {
    price_Change_Events: "价格变动事件",
    add_priceChangeEvent: "添加价格变动事件",
    edit_priceChangeEvent: "编辑价格变动事件",
    view_priceChangeEvent: "查看价格变动事件",
    priceChangeName: "价格变动名称",
    priceChangeRule: "价格变动规则",
    templateName: "模板名称",
    scheduleTime: "预定时间",
    areaFilter: "区域筛选",
    outlet: "门店",
    productFilter: "产品筛选",
    productElement: "产品元素",
    brand: "品牌",
    promotionType: "促销类型",
    templateSelection: "模板选择",
    selectTemplate: "选择用于此价格变动事件的模板",
    schedule: "计划",
    scheduleMode: "计划模式",
    timeZone: "时区",
    startPromotion: "开始促销",
    endPromotion: "结束促销",
    doesNotEnd: "没有结束时间",
    setRepeatCondition: "设置重复条件",
    immediateSync: "立即同步",
    startTime: "开始时间",
    endTime: "结束时间",
    validYear: "有效年份",
    validMonth: "有效月份",
    validDate: "有效日期",
    startDate: "开始日期",
    endDate: "结束日期",
    days: "天",
    dates: "日期",
    approvalStatus: "审批状态",
    allDay: "全天",
  },

  approval: {
    approvalList: "审批列表",
    priceChangeDetails: "价格变动详情",
    viewApprovalRequest: "查看价格变动事件的批准请求",
    approvalEvent: "审批事件",
    maker: "创建者",
    createTime: "创建时间",
    priceChangeName: "价格变动名称",
    productFilter: "产品筛选",
    priceChangeRule: "价格变动规则",
    priceChangeRuleName: "价格变动规则名称",
    templateSelection: "模板选择",
    templateName: "模板名称",
    schedule: "计划",
    timeZone: "时区",
    startTime: "开始时间",
    endTime: "结束时间",
    setRepeatCondition: "设置重复条件",
    includeExcludeDays: "包含/排除一周中的天数",
    approve: "审批",
    reject: "拒绝",
    rejectUpper: "拒绝",
    approveUpper: "批准",
    approvalLog: "审批日志",
    approvalLogEvent: "审批事件",
    approvalLogPerson: "审批人员",
    approvalLogApprovalTime: "审批时间",
    approvalLogStatus: "审批状态",
  },

  synchronized: {
    systemRecords: "系统日志",

    screenID: "屏幕编号",
    outlet: "门店",
    product: "产品",
    deviceSN: "设备序列号",
    status: "状态",
    picture: "图片",

    // 新增
    mobile: "手机",
    address: "地址",
    country: "国家",
    stateProvince: "省份",
    city: "城市",
    noRecordFound: "未找到记录。",
    clear: "清除",
    confirm: "确定",
  },

  price: {
    promotiontype: "请选择促销类型",
    number: "请输入数字",
    maximum_discount: "最大折扣为 100%",
    select_timezone: "请选择时区",
    select_starttime: "请选择开始时间",
    select_time: "请选择时间",
    approved: "批准",
    rejected: "拒绝",
    deleted: "已结束",
    last_template: "上次使用的模板",
    select_times: "选择时间",
  },

  enum: {
    does_not_repeat: "不重复",
    daily: "日",
    weekly: "周",
    monthly: "月",
    annually: "年",
  },
};
