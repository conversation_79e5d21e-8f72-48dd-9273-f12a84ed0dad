# 增强型数据表格组件使用指南

## 概述

`EnhancedDataTable` 是一个基于 Material-UI 的现代化数据表格组件，专为提升用户体验而设计。它具有现代化的视觉风格，与你的应用整体设计保持一致。

## 主要特性

### ✨ 视觉设计
- **现代化风格**: 清爽的卡片式设计，圆角边框，柔和阴影
- **优雅配色**: 与 MUI 主题完美融合的配色方案
- **响应式布局**: 适配不同屏幕尺寸
- **流畅动画**: 悬停效果和状态转换动画

### 🚀 功能特性
- **排序功能**: 支持列排序，带有视觉指示器
- **多选功能**: 支持行选择和全选
- **分页功能**: 完整的分页控制
- **加载状态**: 骨架屏加载效果
- **空状态**: 优雅的空数据展示
- **自定义渲染**: 灵活的单元格内容渲染
- **操作按钮**: 内置操作按钮支持

## 基础用法

```jsx
import EnhancedDataTable from '@/components/EnhancedDataTable';

const MyComponent = () => {
  const columns = [
    {
      field: 'name',
      headerName: '名称',
      minWidth: 200,
      sortable: true,
    },
    {
      field: 'status',
      headerName: '状态',
      minWidth: 100,
      renderCell: ({ value }) => (
        <Chip label={value} color="primary" size="small" />
      ),
    },
  ];

  const rows = [
    { id: 1, name: '示例项目', status: '活跃' },
    { id: 2, name: '测试项目', status: '暂停' },
  ];

  return (
    <EnhancedDataTable
      columns={columns}
      rows={rows}
      checkboxSelection={true}
      pagination={true}
      sortable={true}
    />
  );
};
```

## 高级用法

### 自定义单元格渲染

```jsx
const columns = [
  {
    field: 'avatar',
    headerName: '用户',
    renderCell: ({ value, row }) => (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <Avatar src={value} sx={{ width: 32, height: 32 }}>
          {row.name?.charAt(0)}
        </Avatar>
        <Typography variant="body2">{row.name}</Typography>
      </Box>
    ),
  },
  {
    field: 'actions',
    headerName: '操作',
    renderCell: ({ row }) => (
      <Stack direction="row" spacing={0.5}>
        <IconButton size="small" onClick={() => handleEdit(row)}>
          <EditIcon />
        </IconButton>
        <IconButton size="small" onClick={() => handleDelete(row)}>
          <DeleteIcon />
        </IconButton>
      </Stack>
    ),
  },
];
```

### 状态管理

```jsx
const [selectedRows, setSelectedRows] = useState([]);
const [page, setPage] = useState(0);
const [rowsPerPage, setRowsPerPage] = useState(10);
const [sortBy, setSortBy] = useState('');
const [sortOrder, setSortOrder] = useState('asc');

const handleSort = (field, order) => {
  setSortBy(field);
  setSortOrder(order);
  // 调用API进行排序
};

const handleSelectionChange = (newSelection) => {
  setSelectedRows(newSelection);
};
```

## 属性配置

### 基础属性
| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `columns` | Array | `[]` | 列配置数组 |
| `rows` | Array | `[]` | 数据行数组 |
| `loading` | Boolean | `false` | 加载状态 |
| `checkboxSelection` | Boolean | `false` | 是否显示复选框 |
| `pagination` | Boolean | `true` | 是否显示分页 |
| `sortable` | Boolean | `false` | 是否支持排序 |

### 高级属性
| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `dense` | Boolean | `false` | 紧凑模式 |
| `stickyHeader` | Boolean | `false` | 固定表头 |
| `maxHeight` | String | `"70vh"` | 最大高度 |
| `showSkeleton` | Boolean | `true` | 显示骨架屏 |
| `animateRows` | Boolean | `true` | 行动画效果 |

### 回调函数
| 属性 | 类型 | 描述 |
|------|------|------|
| `onRowClick` | Function | 行点击回调 |
| `onSelectionChange` | Function | 选择变化回调 |
| `onSort` | Function | 排序回调 |
| `onPageChange` | Function | 页码变化回调 |
| `onRowsPerPageChange` | Function | 每页行数变化回调 |

## 列配置

### 基础列配置
```jsx
{
  field: 'name',           // 字段名
  headerName: '名称',      // 表头显示名
  minWidth: 200,          // 最小宽度
  align: 'left',          // 对齐方式
  sortable: true,         // 是否可排序
}
```

### 自定义渲染
```jsx
{
  field: 'status',
  headerName: '状态',
  renderCell: ({ value, row }) => {
    // 自定义渲染逻辑
    return <Chip label={value} />;
  },
}
```

## 样式定制

### 主题定制
```jsx
import { ThemeProvider } from '@mui/material/styles';
import { tableTheme } from '@/components/EnhancedDataTable/tableTheme';

<ThemeProvider theme={tableTheme}>
  <EnhancedDataTable {...props} />
</ThemeProvider>
```

### 自定义样式
```jsx
<EnhancedDataTable
  sx={{
    '& .MuiTableCell-head': {
      backgroundColor: '#f5f5f5',
      fontWeight: 600,
    },
    '& .MuiTableRow-root:hover': {
      backgroundColor: '#f0f7ff',
    },
  }}
/>
```

## 最佳实践

### 1. 性能优化
- 使用 `useMemo` 缓存列配置
- 合理设置 `rowsPerPage` 避免一次加载过多数据
- 启用服务端分页和排序

### 2. 用户体验
- 提供清晰的加载状态
- 使用有意义的空状态消息
- 合理使用动画效果

### 3. 可访问性
- 为操作按钮提供 `Tooltip`
- 使用语义化的 HTML 结构
- 支持键盘导航

## 完整示例

参考 `PictureLibraryTable.jsx` 文件，它展示了如何在实际项目中使用增强型表格组件，包括：

- 复杂的单元格渲染
- 状态管理
- 操作按钮集成
- 工具栏集成
- 国际化支持

这个组件将为你的应用提供现代化、高性能的数据展示体验！
