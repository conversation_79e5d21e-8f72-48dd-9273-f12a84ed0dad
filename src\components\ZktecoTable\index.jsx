import { tableI18n } from "@/util/tableLang";
import { useNavigate } from "react-router-dom";
import React, { useMemo } from "react";
import ZKPagination from "./ZKPagination";
import EmptyFallBack from "./EmptyFallBack";
import PropTypes from "prop-types";
import { useTranslation } from "react-i18next";
import MaterialReactTable from "material-react-table";
import {
  tablePaperSx,
  tableHeadRowSx,
  tableBodyCellSx,
  dashedBorderSx,
  tableBodyTextSx,
} from "./utils";
import { Grid, Tooltip } from "@mui/material";
import { toast } from "react-toastify";
import MainCard from "@c/MainCard.jsx";
import ZKTableTopActions from "./ZKTableTopActions";
import ZKTableRowActions from "./ZKTableRowActions";

const wrapCellWithTooltip = (column) => {
  // 如果列配置中设置了 disableTooltip，则不包装 tooltip
  if (column.disableTooltip) {
    return column;
  }

  const originalCell = column.Cell || ((props) => props.value);

  return {
    ...column,
    Cell: ({ cell, row }) => (
      <Tooltip
        title={originalCell({ cell, row }) || row.original[column.accessorKey]}
        arrow
        placement="bottom">
        <span>
          {originalCell({ cell, row }) || row.original[column.accessorKey]}
        </span>
      </Tooltip>
    ),
  };
};

const ZktecoTable = (props) => {
  const {
    columns = [],
    data = [],
    rowCount = 0,
    headerTitle = "",
    isLoading = false,
    isError = false,
    isRefetching = false,
    state = {},
    paginationProps = {},
    topActions = {},
    actionHandlers = {},
    isShowAction = {},
    renderRowActions = () => null,
    pathRoute,
    actionColumnWidth = 160,
    enablePagination = true,
    manualPagination = true,
    enableRowActions = true,
    renderBottomToolbarCustomActions,
    actionColumnMaxHeight = 120, // 新增属性控制操作列最大高度
    ...otherProps
  } = props;

  const {
    currentPage = 1,
    rowsPerPage = 5,
    onPageChange = () => {},
    onPageSizeChange = () => {},
  } = paginationProps;

  const {
    showAdd = false,
    showRefresh = true,
    showDownload = false,
    showUpload = false,
    onAdd = () => pathRoute && navigate(pathRoute),
    onRefresh = () => {
      otherProps?.loadDada?.();
      toast.success(t("common.common_refresh_success"));
    },
    onDownload = () => {},
    onUpload = () => {},
  } = topActions;

  const { t } = useTranslation();
  const navigate = useNavigate();

  const wrappedColumns = useMemo(
    () => props.columns.map(wrapCellWithTooltip),
    [props.columns]
  );

  return (
    <Grid
      sx={{
        height: "100%",
        bgcolor: "#FFF",
        borderRadius: "15px",
        width: "100%",
      }}>
      <MainCard sx={{ height: "100%", width: "100%" }}>
        {(showAdd || showRefresh || showDownload || showUpload) && (
          <ZKTableTopActions
            headerTitle={headerTitle}
            showAdd={showAdd}
            showRefresh={showRefresh}
            showDownload={showDownload}
            showUpload={showUpload}
            addCallback={onAdd}
            refreshCallback={onRefresh}
            downCallBack={onDownload}
            uploadCallBack={onUpload}
          />
        )}

        <MaterialReactTable
          columns={wrappedColumns}
          data={data}
          rowCount={rowCount}
          enablePagination={false}
          manualPagination={manualPagination}
          enableRowActions={enableRowActions}
          renderRowActions={({ row }) => (
            <ZKTableRowActions
              row={row}
              renderRowActions={renderRowActions}
              isShowAction={isShowAction}
              handlers={actionHandlers}
            />
          )}
          renderBottomToolbarCustomActions={() =>
            renderBottomToolbarCustomActions ? (
              renderBottomToolbarCustomActions()
            ) : (
              <ZKPagination
                totalRecords={rowCount}
                rowsPerPage={rowsPerPage}
                currentPage={currentPage}
                onPageChange={onPageChange}
                onPageSizeChange={onPageSizeChange}
              />
            )
          }
          displayColumnDefOptions={{
            "mrt-row-actions": {
              header: t("common.common_relatedOp"),
              size: actionColumnWidth,
              overflowX: "auto",
            },
          }}
          state={{
            columnPinning: { right: ["mrt-row-actions"] },
            // 加载状态
            isLoading: isLoading,
            rowSelection: true,
            // 重新拉取
            showProgressBars: isRefetching,
            showAlertBanner: isError,
            ...state,
          }}
          enableStickyHeader
          enableClickToCopy
          enableTopToolbar={false}
          enableBottomToolbar
          enableColumnActions={false}
          enableColumnOrdering={false}
          enableColumnResizing
          enableEditing={false}
          enableExpanding={false}
          enableFilters={false}
          enableGlobalFilter={false}
          enableFullScreenToggle={false}
          enableMultiSort={false}
          enableRowOrdering={false}
          filterFromLeafRows
          layoutMode="grid"
          localization={tableI18n}
          manualFiltering
          muiTablePaperProps={{ elevation: 0, sx: tablePaperSx }}
          muiTableHeadRowProps={{ sx: tableHeadRowSx }}
          muiTableBodyCellProps={{ sx: tableBodyCellSx }}
          muiTableContainerProps={{ sx: { maxHeight: "100%" } }}
          mantinePaperProps={{ shadow: "none", sx: dashedBorderSx }}
          mantineTableContainerProps={{
            sx: { maxHeight: "200px", ...tableBodyTextSx },
          }}
          mantinePaginationProps={{
            rowsPerPageOptions: [5, 10, 20],
            withEdges: true,
            radius: "xl",
            size: "lg",
            showRowsPerPage: true,
          }}
          paginationDisplayMode="mantine"
          positionToolbarAlertBanner="none"
          renderEmptyRowsFallback={() => <EmptyFallBack />}
          muiToolbarAlertBannerProps={
            isError
              ? { color: "error", children: t("table.loading_error") }
              : undefined
          }
          {...otherProps}
        />
      </MainCard>
    </Grid>
  );
};

ZktecoTable.propTypes = {
  columns: PropTypes.array.isRequired,
  data: PropTypes.array,
  rowCount: PropTypes.number,
  headerTitle: PropTypes.string,
  isLoading: PropTypes.bool,
  isError: PropTypes.bool,
  state: PropTypes.object,
  actionColumnWidth: PropTypes.number,
  actionColumnMaxHeight: PropTypes.number, // 新增属性
  renderRowActions: PropTypes.func,
  renderBottomToolbarCustomActions: PropTypes.func,
  pathRoute: PropTypes.string,
  isShowAction: PropTypes.object,
  actionHandlers: PropTypes.shape({
    handlerView: PropTypes.func,
    handlerEditor: PropTypes.func,
    handlerUserSetting: PropTypes.func,
    handlerUnion: PropTypes.func,
    handlerSendEmail: PropTypes.func,
    onDelete: PropTypes.func,
  }),
};

export default ZktecoTable;
