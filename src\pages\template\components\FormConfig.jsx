export const getFormConfig = (t, resolution, modelList) => {
  const direction = [
    { id: "0", value: t("dictionary.vertical") },
    { id: "1", value: t("dictionary.horizontal") },
  ];

  const templateTypeOptions = [
    { id: "0", value: t("dictionary.generaluse") },
    { id: "1", value: t("dictionary.discount") },
    { id: "2", value: t("dictionary.byunit") },
    { id: "3", value: t("dictionary.by_value") },
    { id: "4", value: t("dictionary.promotion") },
  ];

  let formConfig = [
    {
      name: "name",
      label: t("template.name"),
      type: "input",
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("tips_template.placeholder_name"),
        },
      ],
    },

    {
      name: "resolution",
      label: t("template.resolution"),
      type: "autoComplate",
      options: resolution,
      typeValue: "1",
      placeholder: t("tips_template.placeholder_resolution"),
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("tips.required"),
        },
      ],
    },

    {
      name: "model",
      label: t("template.screen_model"),
      type: "autoComplate",
      options: modelList,
      typeValue: "1",
      placeholder: t("tips_template.placeholder_screen_model"),
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("tips.required"),
        },
      ],
    },

    {
      name: "orientation",
      label: t("template.screen_model"),
      type: "autoComplate",
      options: direction,
      typevalue: "3",
      placeholder: t("tips_template.placeholder_screen_direction"),
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("tips.required"),
        },
      ],
    },

    {
      name: "promotionType",
      label: t("template.type"),
      type: "autoComplate",
      typevalue: "3",
      options: templateTypeOptions,
      placeholder: t("tips_template.placeholder_type"),
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("tips.required"),
        },
      ],
    },
  ];

  return formConfig;
};
