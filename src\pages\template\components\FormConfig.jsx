export const getFormConfig = (t, resolution, modelList) => {
  const direction = [
    { id: "0", value: t("dictionary.vertical") },
    { id: "1", value: t("dictionary.horizontal") },
  ];

  const templateTypeOptions = [
    { id: "0", value: t("dictionary.generaluse") },
    { id: "1", value: t("dictionary.discount") },
    { id: "2", value: t("dictionary.byunit") },
    { id: "3", value: t("dictionary.by_value") },
    { id: "4", value: t("dictionary.promotion") },
  ];

  const dataType = [
    "200*200",
    "250*128",
    "296*128",
    "296*152",
    "320*240",
    "400*300",
    "648*480",
    "800*480",
    "960*640",
  ];

  const aaaa = [
    { id: "0", lable: t("dictionary.generaluse") },
    { id: "1", lable: t("dictionary.discount") },
    { id: "2", lable: t("dictionary.byunit") },
    { id: "3", lable: t("dictionary.by_value") },
    { id: "4", lable: t("dictionary.promotion") },
  ];

  const asfdsfsd = [
    { id: "0", name: t("dictionary.generaluse") },
    { id: "1", name: t("dictionary.discount") },
    { id: "2", name: t("dictionary.byunit") },
    { id: "3", name: t("dictionary.by_value") },
    { id: "4", name: t("dictionary.promotion") },
  ];

  const asfsdfsdf = [
    { value: "0", name: t("dictionary.generaluse") },
    { value: "1", name: t("dictionary.discount") },
    { value: "2", name: t("dictionary.byunit") },
    { value: "3", name: t("dictionary.by_value") },
    { value: "4", name: t("dictionary.promotion") },
  ];

  console.log("rrrrrrrrrrrrrrrr", resolution);

  let formConfig = [
    {
      name: "name",
      label: t("template.name"),
      type: "input",
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("tips_template.placeholder_name"),
        },
      ],
    },

    {
      name: "resolution",
      label: t("template.resolution"),
      type: "autoComplate",
      options: resolution,
      typeValue: "1",
      placeholder: t("tips_template.placeholder_resolution"),
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("tips.required"),
        },
      ],
    },

    {
      name: "model",
      label: t("template.screen_model"),
      type: "autoComplate",
      options: modelList,
      typeValue: "1",
      placeholder: t("tips_template.placeholder_screen_model"),
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("tips.required"),
        },
      ],
    },

    {
      name: "orientation",
      label: t("template.screen_model"),
      type: "autoComplate",
      options: direction,
      typevalue: "3",
      placeholder: t("tips_template.placeholder_screen_direction"),
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("tips.required"),
        },
      ],
    },

    {
      name: "promotionType",
      label: t("template.type"),
      type: "autoComplate",
      typevalue: "3",
      options: templateTypeOptions,
      placeholder: t("tips_template.placeholder_type"),
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("tips.required"),
        },
      ],
    },
  ];

  return formConfig;
};
