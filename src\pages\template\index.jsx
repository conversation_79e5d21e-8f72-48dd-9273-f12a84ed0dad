import React, { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { toast } from "react-toastify";
import LayoutList from "@/components/ListLayout.jsx";
import PreView from "../editor/PreView";
import Search from "./components/Search";

import { debounce } from "lodash-es";
import { getPages } from "@/services/TemplateService.js";
import TableList from "./components/TableList";
function index() {
  const { t } = useTranslation();
  const [preViewOpen, setPreViewOpen] = useState(false);
  const [preViewItem, setPreViewItem] = useState(null);
  const [data, setData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isRefetching, setIsRefetching] = useState(false);
  const [isError, setIsError] = useState(false);
  const [rowCount, setRowCount] = useState(0);

  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 5,
  });

  const buildParams = () => {
    const params = {
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
    };

    return params;
  };

  const getTableData = useCallback(
    debounce(() => {
      if (!data.length) {
        setIsLoading(true);
      } else {
        setIsRefetching(true);
      }

      getPages(buildParams())
        .then((res) => {
          // 设置数据
          setData(res.data.data);
          // 设置总记录数
          setRowCount(res.data.total);
          setIsLoading(false);
          setIsRefetching(false);
        })
        .catch((err) => {
          setIsError(true);
          setIsLoading(false);
          setIsRefetching(false);
        });
    }, 50),
    [pagination.pageIndex, pagination.pageSize]
  );

  useEffect(() => {
    getTableData();
  }, [pagination]);

  const rederTable = () => {
    return (
      <TableList
        data={data}
        isLoading={isLoading}
        isRefetching={isRefetching}
        isError={isError}
        rowCount={rowCount}
        pagination={pagination}
        setPagination={setPagination}
        setPreViewItem={setPreViewItem}
        setPreViewOpen={setPreViewOpen}
        getTableData={getTableData}></TableList>
    );
  };

  return (
    <React.Fragment>
      <LayoutList content={rederTable()}></LayoutList>

      <PerViewTemplate
        preViewOpen={preViewOpen}
        setPreViewItem={setPreViewItem}
        setPreViewOpen={setPreViewOpen}
        templateJson={preViewItem?.templateJson}></PerViewTemplate>
    </React.Fragment>
  );
}

export default index;

const PerViewTemplate = ({
  preViewOpen,
  setPreViewItem,
  setPreViewOpen,
  templateJson,
}) => {
  return (
    <Dialog
      open={preViewOpen}
      onClose={() => {
        setPreViewItem(null);
        setPreViewOpen(false);
      }}
      maxWidth={"md"}
      style={{ backdropFilter: "blur(5px)" }}>
      {templateJson && preViewOpen && (
        <PreView layoutJSON={JSON.parse(templateJson)}></PreView>
      )}
    </Dialog>
  );
};

// <Search
//   filters={filters}
//   resolution={resolution}
//   templateValue={templateValue}
//   setResolution={setResolution}
//   setTemplateValue={setTemplateValue}
//   setFilters={setFilters}></Search>;
