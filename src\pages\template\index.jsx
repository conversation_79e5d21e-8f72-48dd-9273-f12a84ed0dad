import React, { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router";
import { toast } from "react-toastify";
import ListLayout from "@c/ListLayout";
import DataTable from "@c/DataTable";
import Delete from "./components/Delete";
import PreView from "../editor/PreView";
import Search from "./components/Search";
import { getPicPreview, loadData } from "./components/utils";
import { getColums } from "./components/Colums";
import { useStatePermission } from "@/hooks/user";
function index() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const permission = JSON.parse(
    sessionStorage.getItem("USER_INFO")
  )?.permissions;
  const [records, setRecords] = useState([]);
  const [downloadUrls, setDownloadUrls] = useState([]);
  const [confirm, setConfirm] = useState(false);
  const [id, setId] = useState("");
  const [totalRecords, setTotalRecords] = React.useState(0);
  const [resolution, setResolution] = useState(null);
  const [templateValue, setTemplateValue] = useState(null);
  const [loading, setLoading] = useState(false);
  const [preViewOpen, setPreViewOpen] = useState(false);
  const [preViewItem, setPreViewItem] = useState(null);
  const columns = getColums(
    t,
    setConfirm,
    setId,
    setPreViewOpen,
    setPreViewItem
  );

  const [filters, setFilters] = useState({
    page: 1,
    pageSize: 5,
    name: "",
    promotionType: "",
    resolution: "",
  });

  const toolbarProps = {
    add:
      permission &&
      (permission.includes("nt:nutag:template:save") ||
        permission.includes("*:*:*")),
    filter: false,
    refresh: true,
    onAdd: (data) => {
      navigate("/add/template");
    },
    onRefresh: (data) => {
      setFilters({ ...filters });
      loadData();
    },
  };

  const handlePageChange = (e) => {
    setFilters({
      ...filters,
      property: resolution,
      promotionType: templateValue,
      pageSize: filters.pageSize,
      page: e + 1,
    });
  };

  const handlePageSize = (e) => {
    setFilters({
      ...filters,
      resolution: resolution,
      promotionType: templateValue,
      page: 1,
      pageSize: e,
    });
  };

  useEffect(() => {
    loadData(filters, setRecords, setTotalRecords, setDownloadUrls, setLoading);
  }, [filters]);

  useEffect(() => {
    if (!confirm) {
      loadData(
        filters,
        setRecords,
        setTotalRecords,
        setDownloadUrls,
        setLoading
      );
    }
  }, [confirm]);

  return (
    <React.Fragment>
      <Search
        filters={filters}
        resolution={resolution}
        templateValue={templateValue}
        setResolution={setResolution}
        setTemplateValue={setTemplateValue}
        setFilters={setFilters}></Search>

      <ListLayout
        navigateBack={false}
        title={t("")}
        toolbarProps={toolbarProps}
        isShowSearch={false}>
        <DataTable
          columns={columns}
          rows={records}
          page={filters.page - 1}
          totalRecords={totalRecords}
          loading={loading}
          rowsPerPage={filters.pageSize}
          onSelection={() => console.log()}
          onPageChange={(pn) => handlePageChange(pn)}
          onPageSizeChange={(ps) => handlePageSize(ps)}
        />
      </ListLayout>

      <Delete id={id} confirm={confirm} setConfirm={setConfirm}></Delete>

      <PerViewTemplate
        preViewOpen={preViewOpen}
        setPreViewItem={setPreViewItem}
        setPreViewOpen={setPreViewOpen}
        templateJson={preViewItem?.templateJson}></PerViewTemplate>
    </React.Fragment>
  );
}

export default index;

const PerViewTemplate = ({
  preViewOpen,
  setPreViewItem,
  setPreViewOpen,
  templateJson,
}) => {
  return (
    <Dialog
      open={preViewOpen}
      onClose={() => {
        setPreViewItem(null);
        setPreViewOpen(false);
      }}
      maxWidth={"md"}
      style={{ backdropFilter: "blur(5px)" }}>
      {templateJson && preViewOpen && (
        <PreView layoutJSON={JSON.parse(templateJson)}></PreView>
      )}
    </Dialog>
  );
};
