import React, { useEffect, useState } from "react";
import {
  Input<PERSON>abel,
  <PERSON>ack,
  Autocomplete,
  TextField,
  FormHelperText,
  Grid,
} from "@mui/material";
import RequirePoint from "../RequirePoint";
import { useTranslation } from "react-i18next";

function ZkAutocomplete({
  formik = null,
  placeholder = "",
  label = "",
  name = "",
  error = null,
  disabled = false,
  options = [],
  typeValue = "0",
  labelpostion = "column",
  spacing = 1,
  width = "100%",
  fontSize = "18px",
  readonly = false,
  required = false,
  isValue = "false",
  sx,
  ...otherProps
}) {
  const [data, setData] = useState(null);
  const { t } = useTranslation();

  // Define a mapping for typeValue to avoid repetitive if-else conditions
  const getOptionValue = (option) => {
    const mapping = {
      1: option,
      2: option?.id,
      3: option?.value,
      4: option?.label,
      5: option?.name,
    };
    return mapping[typeValue] || option?.value;
  };

  useEffect(() => {
    if (isValue == "true") {
      let data = options?.find((item) => item.value == formik.values[name]);
      setData(data);
      formik?.setFieldValue(name, data?.value);
    } else {
      let data = options.find((item) => item.id == formik.values[name]);
      setData(data);
      formik?.setFieldValue(name, data?.id);
    }
  }, [options]);

  useEffect(() => {
    if (isValue == "zoon") {
      let data = options?.find((item) => item.value == formik.values[name]);
      setData(data);
      formik?.setFieldValue(name, data?.value);
    }
  }, [formik.values[name], options]);

  const handleChange = (event, newValue) => {
    if (isValue == "true") {
      setData(newValue);

      const fieldValue = getOptionValue(newValue);
      formik?.setFieldValue(name, newValue.value);
    } else {
      setData(newValue);
      formik?.setFieldValue(name, newValue?.id);
    }
  };

  return (
    <Stack spacing={1} sx={{ width: "100%" }}>
      {label && (
        <Stack
          direction={labelpostion === "left" ? "row" : "column"}
          alignItems={labelpostion === "left" ? "flex-start" : ""}
        >
          <InputLabel
            htmlFor={`CmpAutoComPlete_${name}`}
            shrink
            sx={{
              color: "#474b4fcc",
              fontSize: "18px",
            }}
          >
            {label} {required && <RequirePoint />}
          </InputLabel>
        </Stack>
      )}
      <Stack
        sx={{
          flexGrow: 1,
          width: "100%",
        }}
      >
        <Grid
          sx={{
            marginTop: label ? "0px" : "28px",
          }}
        >
          <Autocomplete
            id={`CmpAutoComPlete_${name}`}
            disablePortal
            fullWidth
            options={options || []}
            value={data || null}
            label={""}
            onChange={handleChange}
            disabled={disabled}
            // isOptionEqualToValue={(option, value) => {
            //   return getOptionValue(option) === getOptionValue(value);
            // }}
            isOptionEqualToValue={(option, value) => option.id === value.id}
            getOptionLabel={(option) => getOptionValue(option) || ""}
            noOptionsText={t("common.no_options")}
            renderOption={(props, option) => (
              <li {...props} key={option.id}>
                {getOptionValue(option)}
              </li>
            )}
            renderInput={(params) => (
              <TextField
                {...params}
                readOnly={readonly}
                placeholder={placeholder}
                sx={{
                  "& .MuiOutlinedInput-input": {
                    height: "38px",
                    borderRadius: "25px",
                    fontSize: "16px",
                    marginTop: "-5px",
                  },

                  "& .MuiInputBase-root ": {
                    height: "46px",
                    marginTop: "-5px",
                  },
                }}
              />
            )}
            {...otherProps}
          />

          {(formik?.touched[name] && formik?.errors[name]) || error ? (
            <FormHelperText error id={`standard-weight-helper-text-${name}`}>
              {formik?.errors[name] || error}
            </FormHelperText>
          ) : null}
        </Grid>
      </Stack>
    </Stack>
  );
}

export default ZkAutocomplete;
