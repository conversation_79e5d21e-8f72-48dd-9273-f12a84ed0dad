import React, { useState, useEffect } from "react";
import VisibilityIcon from "@a/images/View_Icon.svg?react";
import EditIcon from "@a/images/Edit_Icon.svg?react";
import DeleteIcon from "@a/images/Delete_Icon.svg?react";
import IconHandaler from "@c/IconHandaler";
import CommonUtil from "../../util/CommonUtils";
import DataTable from "@c/DataTable";
import ListLayout from "@c/ListLayout";
import { useNavigate } from "react-router-dom";
import { deletePriceChangeEvent, getPriceChangeEvents } from "@s/price";
import { useTranslation } from "react-i18next";
import { useSnackbar } from "notistack";
import { useStatePermission } from "@/hooks/user";
import { color } from "echarts";
export default function PriceChangeEvent() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const permission = JSON.parse(
    sessionStorage.getItem("USER_INFO")
  )?.permissions;
  const [loading, setLoading] = useState(false);
  const [records, setRecords] = useState([]);
  const [totalRecords, setTotalRecords] = useState(0);
  const [confirm, setConfirm] = useState(false);
  const [id, setId] = useState("");
  const [open, setOpen] = useState(false);

  const roleName = localStorage.getItem("ROLE_NAME");
  let isAllowedCreate = true;
  let isAllowedView = true;
  let isAllowedEdit = true;
  let isAllowedDelete = true;
  const [filters, setFilters] = useState({
    pageNumber: 1,
    pageSize: 5,
  });

  const defaultFilters = {
    pageNumber: 1,
    pageSize: 5,
  };

  let templateTypeOptions = [
    { id: "0", value: t("dictionary.generaluse") },
    { id: "1", value: t("dictionary.discount") },
    { id: "2", value: t("dictionary.byunit") },
    { id: "3", value: t("dictionary.by_value") },
    { id: "4", value: t("dictionary.promotion") },
  ];

  const getTemplateType = (value) => {
    const option = templateTypeOptions.find((option) => option.id == value);
    return option ? option.value : ""; // 如果找到了对应的值，返回对应的 `value`，否则返回空字符串
  };

  const loadData = () => {
    getPriceChangeEvents(filters).then((res) => {
      setLoading(true);
      if (res?.code === "00000000") {
        setRecords(res?.data?.data);
        setTotalRecords(res?.data?.total);
        setLoading(true);
      } else {
        setRecords([]);
        setTotalRecords(0);
        setLoading(false);
      }
    });
  };

  useEffect(() => {
    loadData();
  }, [filters]);

  if (roleName === "ADMIN") {
    isAllowedCreate =
      permission &&
      (permission.includes("nt:nutag:price_event:save") ||
        permission.includes("*:*:*"));
    isAllowedView =
      permission &&
      (permission.includes("nt:nutag:price_event:info") ||
        permission.includes("*:*:*"));
    isAllowedEdit =
      permission &&
      (permission.includes("nt:nutag:price_event:update") ||
        permission.includes("*:*:*"));
    isAllowedDelete =
      permission &&
      (permission.includes("nt:nutag:price_event:delete") ||
        permission.includes("*:*:*"));
  }
  const approvalStatus = (status) => {
    if (status) {
      if (status == "2") {
        return <Typography color={"green"}>{t("price.approved")}</Typography>;
      }
      if (status == "3") {
        return <Typography color={"red"}>{t("price.rejected")}</Typography>;
      }
      if (status == "4") {
        return <Typography color={"#ebba34"}>{t("price.deleted")}</Typography>;
      } else {
        return <Typography color={"gray"}>{t("common.pending")}</Typography>;
      }
    }
  };

  const columns = [
    {
      field: "name",
      headerName: `${t("events.priceChangeName")}`,
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (e) => (
        <>
          <Tooltip title={e.row.name} arrow placement="bottom">
            <span>{CommonUtil.formatLongText(e.row.name)}</span>
          </Tooltip>
        </>
      ),
    },
    {
      field: "product",
      headerName: `${t("table_product.product_name")}`,
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (e) => (
        <>
          <Tooltip title={e.row.productName} arrow placement="bottom">
            <span>{CommonUtil.formatLongText(e.row.productName)}</span>
          </Tooltip>
        </>
      ),
    },
    {
      field: "promotionType",
      headerName: `${t("events.priceChangeRule")}`,
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (e) => (
        <>
          <Tooltip
            title={getTemplateType(e.row.promotionType)}
            arrow
            placement="bottom">
            <span>{getTemplateType(e.row.promotionType)}</span>
          </Tooltip>
        </>
      ),
    },
    {
      field: "templateId",
      headerName: `${t("events.templateName")}`,
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (e) => (
        <Tooltip title={e?.row?.templateName} arrow placement="bottom">
          <span>{CommonUtil.formatLongText(e?.row?.templateName)}</span>
        </Tooltip>
      ),
    },
    {
      field: "startAt",
      headerName: `${t("events.scheduleTime")}`,
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (e) => (
        <Tooltip
          title={dayjs(e.row.startAt).format("YYYY-MM-DD HH:mm:ss")}
          arrow
          placement="bottom">
          <span>
            {CommonUtil.formatLongText(
              dayjs(e.row.startAt).format("YYYY-MM-DD HH:mm:ss")
            )}
          </span>
        </Tooltip>
      ),
    },
    {
      field: "approvalStatus",
      headerName: `${t("events.approvalStatus")}`,
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (e) => (
        <>
          <Tooltip
            title={approvalStatus(e.row.approvalStatus)}
            arrow
            sx={{
              color: "#fff",
            }}
            placement="bottom">
            <span>
              {CommonUtil.formatLongText(approvalStatus(e.row.approvalStatus))}
            </span>
          </Tooltip>
        </>
      ),
    },
    {
      headerName: `${t("common.actions")}`,
      headerAlign: "center",
      align: "center",
      renderCell: (e) => {
        return (
          <IconHandaler>
            {isAllowedView && (
              <Tooltip
                title={t("common.view")}
                arrow
                id="PriceChangeEvent-tooltip-1">
                <VisibilityIcon
                  onClick={() => handleActions("View", e.row.id)}
                  style={{
                    alignSelf: "center",
                    paddingTop: "0px",
                    cursor: "pointer",
                    opacity: "0.6",
                    height: "17px",
                    width: "20px",
                    padding: "2px",
                  }}
                />
              </Tooltip>
            )}
            {isAllowedEdit && (
              <Tooltip
                title={t("common.edit")}
                sx={{ marginLeft: 1 }}
                id="PriceChangeEvent-tooltip-2">
                <EditIcon
                  onClick={() => handleActions("Edit", e.row)}
                  style={{
                    alignSelf: "center",
                    paddingTop: "0px",
                    cursor: "pointer",
                    opacity: "0.6",
                    height: "17px",
                    width: "20px",
                    padding: "2px",
                  }}
                />
              </Tooltip>
            )}
            {isAllowedDelete && (
              <Tooltip
                title={t("common.delete")}
                sx={{ marginLeft: 1 }}
                id="PriceChangeEvent-tooltip-3">
                <DeleteIcon
                  onClick={() => handleActions("Delete", e.row.id)}
                  style={{
                    alignSelf: "center",
                    paddingTop: "0px",
                    cursor: "pointer",
                    opacity: "0.6",
                    height: "17px",
                    width: "20px",
                    padding: "2px",
                  }}
                />
              </Tooltip>
            )}
          </IconHandaler>
        );
      },
    },
  ];

  const handleActions = (action, data) => {
    if (action === "View") {
      navigate("/view/price-change-event", {
        state: { ...data, action: action, id: data },
      });
    }
    if (action === "Edit") {
      localStorage.setItem("event", JSON.stringify(data));
      navigate("/add/price-change-event", {
        state: { ...data, action: action },
      });
    }
    if (action === "Delete") {
      setId(data);
      setConfirm(true);
    }
  };

  const handlePageChange = (e) => {
    setFilters({
      ...filters,
      pageNumber: e + 1,
    });
  };

  const handlePageSize = (e) => {
    setFilters({
      ...defaultFilters,
      pageNumber: defaultFilters.pageNumber,
      pageSize: e,
    });
  };

  const toolbarProps = {
    add: isAllowedCreate,
    filter: false,
    refresh: true,
    onAdd: (data) => {
      navigate("/add/price-change-event");
    },
    onRefresh: (data) => {
      loadData();
    },
    // onFilter: (data) => {
    //   console.log("onFilter");
    // },
  };

  return (
    <ListLayout
      title={t("events.price_Change_Events")}
      toolbarProps={toolbarProps}
      navigateBack={false}>
      <DataTable
        columns={columns}
        rows={records}
        page={filters.pageNumber}
        totalRecords={totalRecords}
        loading={loading}
        rowsPerPage={filters.pageSize}
        onPageChange={(pn) => handlePageChange(pn)}
        onPageSizeChange={(ps) => handlePageSize(ps)}
        onSelection={() => console.log("")}
      />

      <DetelePriceEvent
        open={open}
        setOpen={setOpen}
        confirm={confirm}
        setConfirm={setConfirm}
        id={id}
        loadData={loadData}></DetelePriceEvent>
    </ListLayout>
  );
}

const DetelePriceEvent = (props) => {
  const { open, setOpen, confirm, setConfirm, id, loadData } = props;
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  const handleDelete = (id) => {
    deletePriceChangeEvent(id).then((res) => {
      if (res?.data?.code === "LVLI0002") {
        enqueueSnackbar(t("tips_events.deleted"), { variant: "success" });
        setConfirm(false);
        loadData();
        navigate("REACT_PRICE_CHANGE_EVENT");
      } else {
        enqueueSnackbar(t(res?.data?.message), { variant: "error" });
        setConfirm(false);
      }
    });
  };

  return (
    <>
      <Dialog
        open={open}
        onClose={() => {
          setOpen(false);
          setConfirm(false);
        }}
        maxWidth={"md"}
        style={{ backdropFilter: "blur(5px)" }}>
        <DialogContent>
          <ImagePopUp />
        </DialogContent>
      </Dialog>
      <Dialog
        open={confirm}
        onClose={() => {
          setOpen(false);
          setConfirm(false);
        }}
        maxWidth="md">
        <DialogTitle>
          <Typography variant="subtitle2">
            {t("tips.selected_delete_record")}
          </Typography>
        </DialogTitle>
        <Grid
          item
          xs={12}
          sm={12}
          md={12}
          lg={12}
          xl={12}
          padding={"10px"}
          justifyContent={"center"}>
          <Box display="flex" justifyContent="center">
            <Button
              variant="outlined"
              color="primary"
              style={{ fontSize: "normal normal normal 14px / 22px Roboto" }}
              onClick={() => {
                setOpen(false);
                setConfirm(false);
              }}
              sx={{ marginRight: 2 }}>
              {t("common.cancel")}
            </Button>
            <Button
              variant="contained"
              color="primary"
              style={{
                background:
                  "transparent linear-gradient(270deg, #1487CA 0%, #78BC27 100%)",
                color: "#FFFFFF",
                fontSize: "normal normal normal 14px / 22px Roboto",
                width: "80px",
              }}
              onClick={() => handleDelete(id)}>
              {t("common.delete")}
            </Button>
          </Box>
        </Grid>
      </Dialog>
    </>
  );
};

const ImagePopUp = () => {
  const htmlContentRef = useRef();
  const [htmlContent, setHtmlContent] = useState("");
  return (
    <div
      id="htmlContent"
      ref={htmlContentRef}
      dangerouslySetInnerHTML={{ __html: htmlContent }}
      style={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
      }}></div>
  );
};
