/**
 * 微前端导航工具函数
 * 解决在qiankun等微前端环境中导航时的重新加载问题
 */

/**
 * 检查是否在微前端环境中运行
 * @returns {boolean} - 是否在微前端环境
 */
const isMicroFrontend = () => {
  return !!(window.__POWERED_BY_QIANKUN__ || window.qiankunProps);
};

/**
 * 安全的微前端导航函数
 * @param {Function} navigate - React Router的navigate函数
 * @param {string} path - 目标路径
 * @param {Object} options - 导航选项
 */
export const safeMicroFrontendNavigate = (navigate, path, options = {}) => {
  try {
    // 在微前端环境中使用特殊的导航策略
    if (isMicroFrontend()) {
      // 方案1: 使用replace避免历史记录堆积
      const navigateOptions = {
        replace: true,
        ...options
      };

      // 延迟导航，确保当前操作完成
      setTimeout(() => {
        navigate(path, navigateOptions);
      }, 100);

    } else {
      // 非微前端环境使用正常导航
      navigate(path, options);
    }
  } catch (error) {
    console.error('Navigation error in micro-frontend:', error);
    // 降级方案：直接使用window.location
    fallbackNavigation(path);
  }
};

/**
 * 降级导航方案
 * @param {string} path - 目标路径
 */
const fallbackNavigation = (path) => {
  try {
    // 在微前端环境中，可能需要通知主应用进行导航
    if (window.qiankunProps?.actions) {
      // 通过qiankun的全局状态管理进行导航
      window.qiankunProps.actions.setGlobalState({
        navigate: path,
        timestamp: Date.now()
      });
    } else {
      // 最后的降级方案
      const baseUrl = window.location.origin + window.location.pathname.split('/')[1];
      window.location.href = baseUrl + path;
    }
  } catch (error) {
    console.error('Fallback navigation failed:', error);
  }
};

/**
 * 带延迟的导航，适用于需要等待异步操作完成的场景
 * @param {Function} navigate - React Router的navigate函数
 * @param {string} path - 目标路径
 * @param {number} delay - 延迟时间（毫秒）
 * @param {Object} options - 导航选项
 */
export const delayedNavigate = (navigate, path, delay = 500, options = {}) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      safeMicroFrontendNavigate(navigate, path, options);
      resolve();
    }, delay);
  });
};

/**
 * 带成功提示的导航
 * @param {Function} navigate - React Router的navigate函数
 * @param {string} path - 目标路径
 * @param {Function} showToast - toast函数
 * @param {string} message - 成功消息
 * @param {Object} options - 导航选项
 */
export const navigateWithToast = (navigate, path, showToast, message, options = {}) => {
  showToast.success(message);

  // 给用户时间看到toast消息
  setTimeout(() => {
    safeMicroFrontendNavigate(navigate, path, options);
  }, 1000);
};

/**
 * 检查当前是否可以安全导航
 * @returns {boolean}
 */
export const canSafelyNavigate = () => {
  try {
    // 检查是否有正在进行的异步操作
    const hasOngoingOperations = document.querySelector('[data-loading="true"]');

    // 检查是否有未保存的更改
    const hasUnsavedChanges = document.querySelector('[data-dirty="true"]');

    return !hasOngoingOperations && !hasUnsavedChanges;
  } catch (error) {
    console.warn('Cannot determine navigation safety:', error);
    return true; // 默认允许导航
  }
};

/**
 * 智能导航 - 根据环境和状态选择最佳导航方式
 * @param {Function} navigate - React Router的navigate函数
 * @param {string} path - 目标路径
 * @param {Object} options - 导航选项
 */
export const smartNavigate = (navigate, path, options = {}) => {
  if (!canSafelyNavigate() && !options.force) {
    console.warn('Navigation blocked due to ongoing operations');
    return false;
  }

  safeMicroFrontendNavigate(navigate, path, options);
  return true;
};

/**
 * 获取微前端基础路径
 * @returns {string}
 */
export const getMicroFrontendBasePath = () => {
  if (isMicroFrontend() && window.qiankunProps?.container) {
    // 从qiankun容器获取基础路径
    return '/e-price-tag-app';
  }
  return '';
};

/**
 * 构建完整的微前端路径
 * @param {string} relativePath - 相对路径
 * @returns {string}
 */
export const buildMicroFrontendPath = (relativePath) => {
  const basePath = getMicroFrontendBasePath();
  return basePath + relativePath;
};
