import React from 'react';
import { Box, Typography, Paper, Stack } from '@mui/material';
import { useFormik } from 'formik';
import { useTranslation } from 'react-i18next';
import CupAutoComplete from './CupAutoComplete';

const CupAutoCompleteExample = () => {
  const { t } = useTranslation();

  // 示例数据 - 各种不同的数据结构
  const templateTypeOptions = [
    { id: "0", value: t("dictionary.generaluse") },
    { id: "1", value: t("dictionary.discount") },
    { id: "2", value: t("dictionary.byunit") },
    { id: "3", value: t("dictionary.by_value") },
    { id: "4", value: t("dictionary.promotion") },
  ];

  const dataType = [
    "200*200",
    "250*128",
    "296*128",
    "296*152",
    "320*240",
    "400*300",
    "648*480",
    "800*480",
    "960*640",
  ];

  const labelOptions = [
    { id: "0", label: "通用标签" },
    { id: "1", label: "折扣标签" },
    { id: "2", label: "单位标签" },
    { id: "3", label: "价值标签" },
    { id: "4", label: "促销标签" },
  ];

  const nameOptions = [
    { id: "0", name: "通用名称" },
    { id: "1", name: "折扣名称" },
    { id: "2", name: "单位名称" },
    { id: "3", name: "价值名称" },
    { id: "4", name: "促销名称" },
  ];

  const customKeyOptions = [
    { value: "0", name: "自定义键值1" },
    { value: "1", name: "自定义键值2" },
    { value: "2", name: "自定义键值3" },
    { value: "3", name: "自定义键值4" },
    { value: "4", name: "自定义键值5" },
  ];

  const numberOptions = [1, 2, 3, 4, 5];

  // 表单配置
  const formik = useFormik({
    initialValues: {
      templateType: '',
      resolution: '',
      labelField: '',
      nameField: '',
      customKey: '',
      numberValue: '',
    },
    onSubmit: (values) => {
      console.log('Form values:', values);
    },
  });

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" sx={{ mb: 3, fontWeight: 600 }}>
        CupAutoComplete 组件使用示例
      </Typography>

      <Stack spacing={3}>
        {/* 示例1: 标准对象数组 (id + value) */}
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" sx={{ mb: 2 }}>
            示例1: 标准对象数组 (id + value)
          </Typography>
          <CupAutoComplete
            formik={formik}
            name="templateType"
            label="模板类型"
            placeholder="请选择模板类型"
            options={templateTypeOptions}
            required={true}
          />
          <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
            使用默认配置: fieldNames={`{label: 'value', value: 'id', key: 'id'}`}
          </Typography>
        </Paper>

        {/* 示例2: 字符串数组 */}
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" sx={{ mb: 2 }}>
            示例2: 字符串数组
          </Typography>
          <CupAutoComplete
            formik={formik}
            name="resolution"
            label="分辨率"
            placeholder="请选择分辨率"
            options={dataType}
            optionType="primitive"
          />
          <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
            自动检测为原始类型，直接使用字符串值
          </Typography>
        </Paper>

        {/* 示例3: 自定义字段名 (label) */}
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" sx={{ mb: 2 }}>
            示例3: 自定义字段名 (label)
          </Typography>
          <CupAutoComplete
            formik={formik}
            name="labelField"
            label="标签字段"
            placeholder="请选择标签"
            options={labelOptions}
            fieldNames={{
              label: 'label',
              value: 'id',
              key: 'id'
            }}
          />
          <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
            使用 label 字段作为显示文本: fieldNames={`{label: 'label', value: 'id', key: 'id'}`}
          </Typography>
        </Paper>

        {/* 示例4: 自定义字段名 (name) */}
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" sx={{ mb: 2 }}>
            示例4: 自定义字段名 (name)
          </Typography>
          <CupAutoComplete
            formik={formik}
            name="nameField"
            label="名称字段"
            placeholder="请选择名称"
            options={nameOptions}
            fieldNames={{
              label: 'name',
              value: 'id',
              key: 'id'
            }}
          />
          <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
            使用 name 字段作为显示文本: fieldNames={`{label: 'name', value: 'id', key: 'id'}`}
          </Typography>
        </Paper>

        {/* 示例5: 完全自定义键值 */}
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" sx={{ mb: 2 }}>
            示例5: 完全自定义键值
          </Typography>
          <CupAutoComplete
            formik={formik}
            name="customKey"
            label="自定义键值"
            placeholder="请选择自定义项"
            options={customKeyOptions}
            fieldNames={{
              label: 'name',
              value: 'value',
              key: 'value'
            }}
          />
          <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
            使用 value 作为键和值: fieldNames={`{label: 'name', value: 'value', key: 'value'}`}
          </Typography>
        </Paper>

        {/* 示例6: 数字数组 */}
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" sx={{ mb: 2 }}>
            示例6: 数字数组
          </Typography>
          <CupAutoComplete
            formik={formik}
            name="numberValue"
            label="数字选择"
            placeholder="请选择数字"
            options={numberOptions}
            optionType="primitive"
          />
          <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
            数字数组，自动检测为原始类型
          </Typography>
        </Paper>

        {/* 向后兼容示例 */}
        <Paper sx={{ p: 3, backgroundColor: '#FFF3E0' }}>
          <Typography variant="h6" sx={{ mb: 2 }}>
            向后兼容: 使用旧的 typevalue 属性
          </Typography>
          <Stack spacing={2}>
            <CupAutoComplete
              formik={formik}
              name="oldType1"
              label="旧方式 - typevalue=1 (字符串)"
              placeholder="字符串选项"
              options={dataType}
              typevalue="1"
            />
            <CupAutoComplete
              formik={formik}
              name="oldType2"
              label="旧方式 - typevalue=2 (id)"
              placeholder="ID选项"
              options={templateTypeOptions}
              typevalue="2"
            />
          </Stack>
          <Typography variant="caption" color="warning.main" sx={{ mt: 1, display: 'block' }}>
            ⚠️ 旧的 typevalue 方式仍然支持，但建议使用新的 fieldNames 配置
          </Typography>
        </Paper>

        {/* 当前表单值显示 */}
        <Paper sx={{ p: 3, backgroundColor: '#F3E5F5' }}>
          <Typography variant="h6" sx={{ mb: 2 }}>
            当前表单值
          </Typography>
          <pre style={{ fontSize: '12px', overflow: 'auto' }}>
            {JSON.stringify(formik.values, null, 2)}
          </pre>
        </Paper>
      </Stack>
    </Box>
  );
};

export default CupAutoCompleteExample;
