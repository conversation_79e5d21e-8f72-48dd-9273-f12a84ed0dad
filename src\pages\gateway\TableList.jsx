import React, { useMemo } from "react";
import { useNavigate } from "react-router-dom";
import ZktecoTable from "@c/ZktecoTable";
import { useTranslation } from "react-i18next";
import { useConfirm } from "@/components/zkconfirm";
import { toast } from "react-toastify";
import { deleteGateway } from "@s/gateway.js";
import CommonUtil from "@u/CommonUtils";
function TableList({
  data,
  isLoading,
  isRefetching,
  isError,
  rowCount,
  pagination,
  setPagination,
  getTableData,
}) {
  const { t } = useTranslation();

  const navigate = useNavigate();
  const confirmFn = useConfirm();

  const permission = JSON.parse(
    sessionStorage.getItem("USER_INFO")
  )?.permissions;

  const columns = useMemo(
    () => [
      {
        accessorKey: "sn",
        header: t("screen.gatewaySerialNumber"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },
      {
        accessorKey: "deviceAlias",
        header: t("screen.gatewayName"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },

      {
        accessorKey: "deviceModel",
        header: t("screen.deviceModel"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },

      {
        accessorKey: "protocolType",
        header: t("screen.deviceType"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },

      {
        accessorKey: "outletName",
        header: t("screen.outlet"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },

      {
        accessorKey: "status",
        header: t("screen.gatewayRunningStatus"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
        Cell: ({ row }) => (
          <Tooltip
            title={
              row.original.status == 1 ? t("menu.online") : t("menu.offline")
            }
            arrow
            placement="bottom">
            <span
              style={{ color: row.original.status == "1" ? "green" : "red" }}>
              {CommonUtil.formatLongText(
                row.original.status == 1 ? t("menu.online") : t("menu.offline")
              )}
            </span>
          </Tooltip>
        ),
      },
    ],
    []
  );

  // 删除
  const handlerDelete = async (data) => {
    confirmFn({
      title: t("tips.selected_delete_record"),
      confirmationText: t("common.delete"),
      cancellationText: t("common.cancel"),
      description: t("common.common_confirm_delete"),
    })
      .then(() => {
        let ids = [];
        ids.push(data?.id);
        deleteGateway({
          ids: ids,
        }).then((res) => {
          toast.success(res.message);
          getTableData();
        });
      })
      .catch(() => {
        // 用户取消删除操作，不执行任何操作
      });
  };

  const isShowAction = {
    isShowDetele: "nt:nutag:device:delete",
  };

  const handlePageChange = useCallback(
    (pageIndex) => {
      setPagination((prev) => ({ ...prev, pageIndex }));
    },
    [setPagination]
  );

  const handlePageSizeChange = useCallback(
    (pageSize) => {
      setPagination({ pageIndex: 0, pageSize });
    },
    [setPagination]
  );

  const actionHandlers = useMemo(
    () => ({
      Detele: (data) => {
        handlerDelete(data);
      },
    }),
    []
  );

  return (
    <ZktecoTable
      columns={columns}
      data={data}
      rowCount={rowCount}
      isLoading={isLoading}
      isRefetching={isRefetching}
      isError={isError}
      pathRoute="/add/picture-library"
      loadDada={getTableData}
      paginationProps={{
        currentPage: pagination.pageIndex,
        rowsPerPage: pagination.pageSize,
        onPageChange: handlePageChange,
        onPageSizeChange: handlePageSizeChange,
      }}
      topActions={{
        showAdd: false,
      }}
      actionHandlers={actionHandlers}
      isShowAction={isShowAction}
    />
  );
}

export default TableList;
