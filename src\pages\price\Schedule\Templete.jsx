import React, { useState, useEffect } from "react";
import PreView from "./PreView";
import { Grid, Typography } from "@mui/material";

function Templete(props) {
  const { addFormik, templateList, setTemplateJSON } = props;
  const [selectedTemplate, setSelectedTemplate] = useState("");
  const [selectedTemplates, setSelectedTemplates] = useState([]);

  const handleTemplateSelection = (template) => {
    if (selectedTemplate && selectedTemplate !== template.id) {
      setSelectedTemplates([template.id]);
      setSelectedTemplate(template.id);
      addFormik.setFieldValue("templateId", template.id);
      addFormik.setFieldValue("templateName", template.name);
      setTemplateJSON(template?.templateJson);
    } else {
      if (selectedTemplates.includes(template.id)) {
        setSelectedTemplates(
          selectedTemplates.filter((t) => t !== template.id)
        );
        setSelectedTemplate(null);
        addFormik.setFieldValue("templateId", "");
        addFormik.setFieldValue("templateName", "");
      } else {
        setSelectedTemplates([template.id]);
        setSelectedTemplate(template.id);
        addFormik.setFieldValue("templateId", template.id);
        addFormik.setFieldValue("templateName", template.name);
        setTemplateJSON(template?.templateJson);
      }
    }
  };

  return (
    <React.Fragment>
      <Grid
        container
        sx={{
          display: "flex",
          height: "200px",
        }}
        spacing={2}
        mt={12}>
        {templateList?.map((template) => (
          <Grid item key={template.id} style={{ margin: "10px" }}>
            <Typography variant="h6">{template?.name}</Typography>
            <Typography>{template?.screenResolution}</Typography>

            <Grid
              sx={{
                flexDirection: "row",
                display: "flex",
                cursor: "pointer",
                position: "relative",
              }}>
              <div
                onClick={() => handleTemplateSelection(template)}
                style={{
                  border:
                    selectedTemplates &&
                    selectedTemplates.includes(template.id) &&
                    selectedTemplate
                      ? "3px solid #1487CA"
                      : "2px solid #A2A3A3",

                  width: template?.templateJson?.width,
                  height: template?.templateJson?.height,
                }}>
                {selectedTemplates &&
                selectedTemplates.includes(template.id) &&
                selectedTemplate ? (
                  <Tick
                    style={{
                      position: "absolute",
                      top: 0,
                      left: 0,
                      zIndex: 11111,
                    }}
                  />
                ) : null}

                <PreView
                  layoutJSON={JSON.parse(template?.templateJson)}></PreView>
              </div>
            </Grid>
          </Grid>
        ))}
      </Grid>
    </React.Fragment>
  );
}

export default Templete;
