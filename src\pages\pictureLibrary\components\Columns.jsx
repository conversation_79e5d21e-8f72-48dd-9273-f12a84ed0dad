export const getFormConfig = (t, pictureTypeOptions, options) => {
  let formConfig = [
    {
      name: "pictureId",
      label: t("picture_library.id"),
      type: "input",
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("tips_picture.id"),
        },
      ],
    },

    {
      name: "pictureName",
      label: t("picture_library.name"),
      type: "input",
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("tips_picture.name"),
        },
      ],
    },

    {
      name: "pictureType",
      label: t("picture_library.type"),
      type: "autoComplate",
      options: pictureTypeOptions,
      typevalue: 5,
      placeholder: t("picture_library.please_picture_type"),
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("tips.required"),
        },
      ],
    },

    {
      name: "pictureProcessing",
      label: t("picture_library.progressing"),
      type: "autoComplate",
      options: options,
      typevalue: 5,
      placeholder: t("picture_library.please_select"),
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("ptips.required"),
        },
      ],
    },
  ];

  return formConfig;
};
