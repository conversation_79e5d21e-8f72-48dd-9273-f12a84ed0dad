import React, { useEffect } from "react";
import PreView from "./ProductView";
import CommonUtil from "@u/CommonUtils";
import RightViewLayout from "@c/RighViewLayout";
import { useTranslation } from "react-i18next";
import { useNavigate, useLocation } from "react-router-dom";
import { getAllProducts, uploadImage, getProductType } from "@s/common.js";
import { getList, issue } from "@s/screens.js";
import { list, getImage } from "@s/TemplateService.js";
import { toast } from "react-toastify";
function BindProductToScreen() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { state } = useLocation();
  const [screens, setScreens] = useState([]);
  const [screenObjects, setScreenObjects] = useState([]);

  const [products, setProducts] = useState([]);
  const [product, setProduct] = useState();
  const [templates, setTemplates] = useState([]);
  const [template, setTemplate] = useState();
  const [selectedTemplates, setSelectedTemplates] = useState([]);
  const [customPayload, setCustomPayload] = useState({});
  const [tempJSON, settempJSON] = useState(null);
  const [selectJson, setSelectJson] = useState(null);
  const [payload, setPayload] = useState({
    screenData: {},
    eslId: "",
    url: "",
    orientation: "0",
    type: "Immediate",
  });

  useEffect(() => {
    const data = localStorage.getItem("screenIds");
    setScreenObjects(JSON.parse(data));
    getAllProducts().then((res) => {
      setProducts(res?.data);
    });

    console.log("ewwwwwwwwwwwwwwwwwwww", state);

    list({ resolution: state?.resolution }).then((res) => {
      setTemplates(res.data);
    });
  }, []);

  useEffect(() => {
    let params = {
      id: state?.id,
      screenIds: state?.screenIds,
    };
    getList(params).then((res) => {
      setScreens(res?.data);
    });
  }, [state]);

  useEffect(() => {
    if (!customPayload) {
      settempJSON(null);
    } else {
      if (selectJson) {
        let newJSON = JSON.parse(JSON.stringify(selectJson));

        newJSON?.componentList?.forEach((item) => {
          if (customPayload?.hasOwnProperty(item.type)) {
            // 确保值的赋予正确
            if (item.type === "qrCode" || item.type === "barCode") {
              item.value = customPayload[item.type];
              item.text = customPayload[item.type];
            } else {
              item.value = customPayload[item.type];
            }
          }
        });
        settempJSON(newJSON);
      }
    }
  }, [selectedTemplates, customPayload]);

  const handleTemplateChange = (e, v) => {
    if (v?.templateJson) {
      setSelectJson(JSON.parse(v.templateJson));
    } else {
      settempJSON(null);
    }

    setTemplate(v);
    const selectedIdsAndResolutions = v ? { [v.id]: v?.resolution } : {};
    setSelectedTemplates([selectedIdsAndResolutions]);
  };

  const handleSubmit = async () => {
    try {
      // 校验
      if (CommonUtil.isEmpty(product)) {
        enqueueSnackbar("Product Should not be Empty", { variant: "error" });
        return;
      }
      if (selectedTemplates.length == 0) {
        enqueueSnackbar("Template Should not be Empty", { variant: "error" });
        return;
      }

      // 遍历每个模板
      for (let template of selectedTemplates) {
        const templateId = Object.keys(template)[0];

        // 生成图片
        const imageRes = await getImage({
          templateJson: JSON.stringify(tempJSON),
        });

        // 转换图片为 file
        const dataUrl = imageRes?.data?.templateBase64;
        const byteString = atob(dataUrl.split(",")[1]);
        const mimeString = dataUrl.split(",")[0].split(":")[1].split(";")[0];
        const arrayBuffer = new ArrayBuffer(byteString.length);
        const uintArray = new Uint8Array(arrayBuffer);
        for (let i = 0; i < byteString.length; i++) {
          uintArray[i] = byteString.charCodeAt(i);
        }

        const blob = new Blob([arrayBuffer], { type: mimeString });
        const productName = product?.name;
        const file = new File([blob], `${productName}.png`, {
          type: "image/png",
        });

        // 上传图片
        const formData = new FormData();
        formData.append("multipartFile", file);

        const downloadRes = await uploadImage(formData);

        const downloadUrl = downloadRes.data?.url;
        const screenInfos = screenObjects.map((item) => {
          const value = Object.values(item)[0];

          return {
            screenId: value.id,
            dmsDeviceId: value.dmsDeviceId,
          };
        });

        // 所有模板处理完成，执行绑定操作
        const request = {
          productId: product?.id,
          productName: product?.name,
          templateId: templateId,
          url: downloadUrl,
          screenInfos: screenInfos,
        };

        const finalRes = await issue(request);
        if (finalRes?.code == "00000000") {
          toast.success(t("tips_screens.screens_sync_success"));
          navigate("/screens");
        } else {
          toast.error(t("product.BindProductToScreen.036581-0"));
        }
      }
    } catch (error) {
      toast.error("程序错误.");
    }
  };

  return (
    <React.Fragment>
      <RightViewLayout
        title={t("screen.bind_to_screen")}
        navigateBack={"/screens"}>
        <Card elevation={0}>
          <CardContent>
            <Box>
              <Typography>{t("tips_screens.selected_screen")}</Typography>
              <Grid container pt={3}>
                {screens.map((screen) => (
                  <Grid
                    item
                    lg={3}
                    m={1}
                    key={screen?.id}
                    style={{ backgroundColor: "#F7FBFE" }}
                    p={2}>
                    <Typography>
                      {t("product.model")} <span>{screen?.name}</span>
                    </Typography>
                    <Typography color={"#474B4F"} fontSize={"16px"}>
                      {t("product.inc_screen_iD")} <span>{screen?.id}</span>
                    </Typography>
                    <Typography color={"#474B4F"} fontSize={"16px"}>
                      {t("product.position_no")}
                      <span>{screen?.positionNo}</span>
                    </Typography>
                    <Typography color={"#474B4F"} fontSize={"16px"}>
                      {t("product.resolution")}
                      <span>{screen?.resolution}</span>
                    </Typography>
                  </Grid>
                ))}
              </Grid>
            </Box>
            <Box pt={3}>
              <Grid container columnSpacing={2}>
                <Grid item lg={6}>
                  <Typography>
                    {t("tips_screens.choose_Product_display")}
                  </Typography>
                  <Autocomplete
                    noOptionsText={t("tips.no_options")}
                    options={products || []}
                    value={product}
                    onChange={async (e, v) => {
                      setProduct(v);
                      const response = await getProductType(v?.id);
                      const result = response?.data.reduce((acc, item) => {
                        acc[item.name] = item.value;
                        return acc;
                      }, {});

                      setCustomPayload(result);
                      setPayload({
                        ...payload,
                        productId: v?.id,
                      });
                    }}
                    getOptionLabel={(option) => (option ? option?.name : "")}
                    renderInput={(params) => (
                      <TextField {...params} size="small" />
                    )}
                  />
                </Grid>
                <Grid item lg={6}>
                  <Typography>
                    {t("tips_screens.choose_Template_display")}
                  </Typography>
                  <Autocomplete
                    // multiple
                    noOptionsText={t("tips.no_options")}
                    options={templates || []}
                    value={template}
                    disableCloseOnSelect
                    onChange={handleTemplateChange}
                    getOptionLabel={(option) =>
                      option ? `${option.name} - ${option.resolution}` : ""
                    }
                    renderInput={(params) => (
                      <TextField {...params} size="small" />
                    )}
                  />
                </Grid>
              </Grid>
            </Box>
            <Grid container spacing={2} pb={2}>
              <Grid item xs={12}>
                <Box
                  sx={{
                    mt: 2,
                  }}
                  display={"flex"}
                  flexDirection={"row-reverse"}>
                  <Box item pl={2}>
                    <Button
                      id="AddAuthorizationLevel-button-01"
                      variant="contained"
                      size="large"
                      className="text-transform-none"
                      onClick={handleSubmit}
                      style={{
                        size: "medium",
                        borderRadius: "8px",
                        opacity: 1,
                        background:
                          "transparent linear-gradient(270deg, #1487CA 0%, #78BC27 100%) 0% 0% no-repeat padding-box",
                      }}>
                      {t("common.save")}
                    </Button>
                  </Box>
                  <Box item>
                    <Button
                      id="AddAuthorizationLevel-button-02"
                      className="text-transform-none"
                      variant="outlined"
                      onClick={() => navigate("/screens")}
                      size="large">
                      {t("common.back")}
                    </Button>
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        <Grid
          sx={{
            padding: "20px",
            display: "flex",
            marginTop: "20px",
            marginLeft: "20px",
            // justifyContent: "center",
          }}>
          {tempJSON && (
            <div>
              <PreView layoutJSON={tempJSON}> </PreView>
            </div>
          )}
        </Grid>
      </RightViewLayout>
    </React.Fragment>
  );
}

export default BindProductToScreen;
