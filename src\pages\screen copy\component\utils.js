import { getPage } from "@s/screens.js";
import CommonUtil from "@u/CommonUtils";
import { toast } from "react-toastify";
import dayjs from "dayjs";
import i18next from "i18next";

export const getFormattedDate = (date) => {
  const formattedDate = date ? dayjs(date).format("YYYY-MM-DD HH:mm:ss") : "";
  return formattedDate;
};

export const loadData = (filters, setRecords, setTotalRecords, setLoading) => {
  setLoading(true);
  getPage(filters).then((res) => {
    setRecords(res?.data?.data);
    setTotalRecords(res?.data?.total);
    setLoading(false);
  });
};

export const toolbarProps = {
  //add: isAllowedCreate,
  add: false,
  filter: false,
  refresh: true,

  onAdd: (data) => { },
  onRefresh: (data) => {
    setFilters({
      ...defaultFilters,
      pageNumber: 1,
    });
    setSearchValue("");
  },
};

export const handleSelection = (
  ids,
  records,
  setAllSelectedRows,
  setSelectedRows,
  allSelectedRows,
  t
) => {
  const updatedSelectedRows = [];
  const selectedResolution = [];
  let pageIds = records.map((item) => {
    return item.id;
  });
  let newTemp = allSelectedRows.filter((item) => {
    let id = Object.keys(item)[0];
    if (pageIds.indexOf(id) < 0) {
      return true;
    } else {
      return false;
    }
  });
  ids.forEach((id) => {
    const record = records.find((row) => row.id === id);
    if (record) {
      updatedSelectedRows.push({ [id]: record.resolution });

      selectedResolution.push(record.resolution);
      localStorage.setItem("resolution", record.resolution);
    }
  });

  let temp = removeDuplicate([...newTemp, ...updatedSelectedRows]);
  setAllSelectedRows(temp);
  setSelectedRows(updatedSelectedRows);

  // const updatedSelectedRows = [];
  // const selectedResolution = [];
  // let pageIds = records.map((item) => {
  //   return item.id;
  // });
  // let newTemp = allSelectedRows.filter((item) => {
  //   let id = Object.keys(item)[0];
  //   if (pageIds.indexOf(id) < 0) {
  //     return true;
  //   } else {
  //     return false;
  //   }
  // });
  // ids.forEach((id) => {
  //   const record = records.find((row) => row.id == id);

  //   if (record) {
  //     updatedSelectedRows.push({ [id]: record });

  //     selectedResolution.push(record.resolution);
  //     localStorage.setItem("resolution", record);
  //   }
  // });

  // if (selectedResolution.length > 1) {
  //   const resolution = selectedResolution[0];
  //   const mismatchedResolution = selectedResolution.some(
  //     (e) => e !== resolution
  //   );
  //   if (mismatchedResolution) {
  //     updatedSelectedRows.pop();
  //     toast.error(t("screen.same_screen"));

  //     return;
  //   }
  // }

  // let temp = removeDuplicate([...newTemp, ...updatedSelectedRows]);
  // setAllSelectedRows(temp);
  // setSelectedRows(updatedSelectedRows);
};

export const handleDataBind = (id, records, navigate) => {
  const updatedSelectedRows = [];

  const record = records.find((row) => row.id === id);
  updatedSelectedRows.push({ [id]: record });

  localStorage.setItem("screenIds", JSON.stringify(updatedSelectedRows));

  navigate("/bind/product", {
    state: {
      id: id,
      resolution: record?.resolution,
    },
  });
};

export const handleBinds = (allSelectedRows, navigate, t) => {
  localStorage.setItem("screenIds", JSON.stringify(allSelectedRows));

  console.log('rrrrrrrrrrrrrrrrrrrr', allSelectedRows);


  if (allSelectedRows.length < 1) {
    toast.error(t("screen.select_one_screen"));
    return;
  }

  // if (allSelectedRows.length > 0) {
  //   toast.error(t("screen.select_online_screen"));
  //   return;
  // }

  // if (allSelectedRows.length > 0) {
  // const isSameData = allSelectedRows.some((obj) => {
  //   const screenId = Object.keys(obj)[0];
  //   const matchedRecord = records.find((item) => item.id == screenId);
  //   const isSame = matchedRecord ? matchedRecord.isSameAdvGroup : null;
  //   return isSame == "0";
  // });

  // if (isSameData) {
  //   toast.error(t("screen.same_advgroup"))
  //   return;
  // }
  // }

  if (allSelectedRows.length > 1) {
    const firstValue = Object.values(allSelectedRows[0])[0];

    const mismatchedResolution = allSelectedRows.every((obj) => {
      const value = Object.values(obj)[0];
      return value === firstValue;
    });

    if (!mismatchedResolution) {
      toast.error(t("screen.same_screen"))
      return;
    }
  }





  navigate("/bind/product", {
    state: {
      screenIds: allSelectedRows.map((item) => Object.keys(item)[0]),
      resolution: allSelectedRows.map((item) => Object.values(item)[0])[0]
    },
  });
};

export const handleBatchUpdate = (setBatchUpdateOpen, allSelectedRows) => {
  if (CommonUtil.isEmpty(allSelectedRows)) {
    toast.error(t("tips_screens.select_one_screen"));
    return;
  }

  setBatchUpdateOpen(true);
};

export const removeDuplicate = (arr) => {
  let key = [];
  let temp = arr.filter((item, index) => {
    let id = Object.keys(item)[0];
    if (key.indexOf(id) < 0) {
      key.push(id);
      return true;
    } else {
      return false;
    }
  });
  return temp;
};

export const calculateBatteryStatus = (batteryState) => {
  if (batteryState === undefined || batteryState === null) {
    return "";
  }
  const v = batteryState / 1000;
  const vs = Math.floor(batteryState / 100) / 10;
  if (v >= 2.3) {
    return i18next.t("screen.normal") + "(" + vs + "V)";
  } else {
    return i18next.t("screen.lowPower") + "(" + vs + "V)";
  }
};
