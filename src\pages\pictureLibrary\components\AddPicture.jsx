import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate, useLocation } from "react-router-dom";
import DropzoneComponent from "@c/DropzoneComponent";
import RightViewLayout from "@c/RighViewLayout";
import { createValidation } from "@c/Config/validationUtils.js";
import { toast } from "react-toastify";
import { add, getDetail, edit } from "@s/PictureService";
import Upload_Image from "@/assets/Icons/Upload_Image.svg?react";
import ZkFormik from "@/components/Config/CmpFormik";
import { getFormConfig } from "./Columns";
import { useFormik } from "formik";
function AddPicture() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { state } = useLocation();
  const [base64, setBase64] = useState(null);
  const [image, setImage] = useState();
  const [loading, setLoading] = React.useState(false);
  const [detail, setDetail] = useState({});
  const [formConfig, setFormConfig] = useState([]);
  const options = [
    {
      id: "0",
      value: t("picture_library.original_image"),
    },
    { id: "1", value: t("picture_library.dithering_image") },
  ];

  const pictureTypeOptions = [
    { id: "0", value: t("picture_library.general_user") },
    { id: "1", value: t("picture_library.company_logo") },
  ];

  useEffect(() => {
    const formConfig = getFormConfig(t, pictureTypeOptions, options);
    setFormConfig(formConfig);
  }, []);

  useEffect(() => {
    if (state?.type == "editor") {
      getDetail(state?.id).then((res) => {
        setDetail(res?.data);
        setBase64(res?.data.url);
      });
    }
  }, []);

  let initialValues = {};
  if (state?.type !== "editor") {
    initialValues = {
      pictureId: "",
      pictureName: "",
      pictureProcessing: "",
      pictureType: "",
    };
  } else {
    initialValues = {
      id: detail?.id,
      pictureId: detail?.pictureId,
      pictureName: detail?.pictureName,
      pictureProcessing: detail?.pictureProcessing,
      pictureType: detail?.pictureType,
    };
  }

  const formik = useFormik({
    initialValues: initialValues,
    validationSchema: createValidation(formConfig),
    enableReinitialize: true, // 允许重新初始化
    onSubmit: (values) => {
      if (state?.type == "editor") {
        setLoading(true);

        let params = {
          ...values,
          multipartFile: image,
          pictureProcessing: values?.pictureProcessing?.id
            ? values?.pictureProcessing?.id
            : values?.pictureProcessing,
          pictureType: values?.pictureType?.id
            ? values?.pictureType?.id
            : values?.pictureType,
        };
        edit(params, state?.id).then((res) => {
          if (res?.code == "00000000") {
            setLoading(false);
            navigate("/picture-library");
            toast.success(res.message);
          } else {
            toast.error(res.message);
            setLoading(false);
          }
        });
      } else {
        let params = {
          ...values,
          multipartFile: image,
          pictureProcessing: values?.pictureProcessing?.id
            ? values?.pictureProcessing?.id
            : values?.pictureProcessing,
          pictureType: values?.pictureType?.id
            ? values?.pictureType?.id
            : values?.pictureType,
        };
        setLoading(true);
        add(params).then((res) => {
          if (res?.code == "00000000") {
            setLoading(false);
            navigate("/picture-library");
            toast.success(res.message);
          } else {
            setLoading(false);
            toast.error(res.message);
          }
        });
      }
    },
  });

  return (
    <React.Fragment>
      <RightViewLayout
        navigateBack={"/picture-library"}
        title={
          state?.type == "editor"
            ? t("picture_library.edit_picture")
            : t("picture_library.add_picture")
        }
        handleSubmit={formik.handleSubmit}
        handleCancle={() => {
          navigate("/picture-library");
        }}
        loading={loading}>
        <Grid sx={{ height: "100%", width: "100%" }}>
          <Grid container spacing={2} mt={2}>
            <Grid item xs={4}>
              <UploadImage
                base64={base64}
                setBase64={setBase64}
                image={image}
                setImage={setImage}></UploadImage>
            </Grid>
            <Grid item xs={8}>
              <ZkFormik
                sx={6}
                formik={formik}
                formConfig={formConfig}></ZkFormik>
            </Grid>
          </Grid>
        </Grid>
      </RightViewLayout>
    </React.Fragment>
  );
}

export default AddPicture;

const UploadImage = (props) => {
  const { t } = useTranslation();
  const { base64, setImage, setBase64 } = props;

  const handleImage = (file, setImage, setBase64) => {
    let maxSize = 2 * 1024 * 1024;

    if (
      file.file.type === "image/png" ||
      file.file.type === "image/jpeg" ||
      file.file.type === "image/bmp"
    ) {
      let fileSize = file.file.size;
      let size = parseInt(fileSize);
      if (size <= maxSize) {
        const img = new Image();
        img.onload = () => {
          setBase64(file.base64);
          setImage(file.file);
        };
        img.src = file.base64;
      } else {
        toast.error(t("tips_picture.file_size"));
      }
    } else {
      toast.error(t("tips_product.upload_image_format"));
    }
  };

  return (
    <React.Fragment>
      <InputLabel
        shrink
        htmlFor="bootstrap-input"
        style={{ color: "#474b4fcc", fontSize: "18px" }}>
        {t("product.product_picture")}
        <span
          style={{
            color: "red",
            fontSize: "18px",
            paddingLeft: "5px",
          }}>
          *
        </span>
      </InputLabel>

      <Box
        style={{
          border: "2px dashed #36C96D",
          borderRadius: "5px",
          backgroundColor: "rgba(54, 201, 109,0.1)",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          flexDirection: "column",
          height: "220px",
          width: "100%",
          cursor: "pointer",
        }}>
        <DropzoneComponent
          getExcelFile={(excelData) =>
            handleImage(excelData, setImage, setBase64)
          }>
          {base64 ? (
            <img
              src={base64}
              alt="Uploaded"
              style={{ maxWidth: "100%", maxHeight: "200px" }}
            />
          ) : (
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
              }}>
              <Upload_Image />
              <Typography
                sx={{
                  fontSize: "12px",
                  textAlign: "center",
                  opacity: "0.8",
                  mt: 1,
                }}>
                {t("tips_picture.image_size")}
              </Typography>
              <Typography
                sx={{
                  fontSize: "12px",
                  textAlign: "center",
                  opacity: "0.8",
                }}>
                {t("tips_picture.support_size")}
              </Typography>
              <Typography
                sx={{
                  fontSize: "12px",
                  textAlign: "center",
                  opacity: "0.8",
                }}>
                {t("tips_picture.resolution_size")}
              </Typography>
            </div>
          )}
        </DropzoneComponent>
      </Box>
    </React.Fragment>
  );
};
