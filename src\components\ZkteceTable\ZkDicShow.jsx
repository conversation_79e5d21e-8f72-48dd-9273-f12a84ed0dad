import { fetchDicByType } from "@/store/reducers/dictionary";
import { useDispatch, useSelector } from "react-redux";
import { useEffect } from "react";
import i18n from "i18next";
const ZkDicShow = ({ type, value }) => {
  const { dic, dicObj } = useSelector((state) => state.dictionary);
  const dispatch = useDispatch();
  useEffect(() => {
    if (!(dic[type] && dic[type].length > 0)) {
      dispatch(
        fetchDicByType({
          type: type,
          isReload: false,
        })
      );
    }
  }, [type]);
  return <span>{dicObj[type]?.[value]?.text || i18n.t("Unknown")}</span>;
};

export default ZkDicShow;
