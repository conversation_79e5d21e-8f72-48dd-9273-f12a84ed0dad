import React, { useMemo } from "react";
import { useNavigate } from "react-router-dom";
import ZktecoTable from "@c/ZktecoTable";
import { useTranslation } from "react-i18next";
import { useConfirm } from "@/components/zkconfirm";
import { toast } from "react-toastify";
import { deletes } from "@/services/TemplateService.js";
import { getPicPreview } from "./utils";
function TableList({
  data,
  isLoading,
  isRefetching,
  isError,
  rowCount,
  pagination,
  setPagination,
  getTableData,
  setPreViewOpen,
  setPreViewItem,
}) {
  const { t } = useTranslation();

  const navigate = useNavigate();
  const confirmFn = useConfirm();

  const permission = JSON.parse(
    sessionStorage.getItem("USER_INFO")
  )?.permissions;

  const columns = useMemo(
    () => [
      {
        accessorKey: "name",
        header: t("template.name"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },

      {
        accessorKey: "model",
        header: t("template.screen_model"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },
      {
        accessorKey: "resolution",
        header: t("template.resolution"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },
      {
        accessorKey: "orientation",
        header: t("template.screen_direction"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
        Cell: ({ row }) => {
          return (
            <span>
              {row.original.orientation == "0"
                ? t("dictionary.vertical")
                : t("dictionary.horizontal")}
            </span>
          );
        },
      },
      {
        accessorKey: "templateType",
        header: t("common.type"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
        Cell: ({ row }) => {
          return (
            <span>
              {row.original.templateType == "0"
                ? t("common.system_default")
                : t("common.personal_template")}
            </span>
          );
        },
      },

      {
        accessorKey: "templateJson",
        header: t("template.preview"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
        Cell: ({ row }) => {
          return (
            <Button
              id="temppreview"
              sx={{ px: 0 }}
              onClick={() => {
                let templateJson = row.original.templateJson;
                if (templateJson) {
                  setPreViewOpen(true);
                  setPreViewItem(row.original);
                } else {
                  toast.info(t("screen.unbound_templates"));
                  getPicPreview(row.original);
                }
              }}>
              {t("common.preview")}
            </Button>
          );
        },
      },
    ],
    []
  );

  // 删除
  const handlerDelete = async (data) => {
    confirmFn({
      title: t("tips.selected_delete_record"),
      confirmationText: t("common.delete"),
      cancellationText: t("common.cancel"),
      description: t("common.common_confirm_delete"),
    })
      .then(() => {
        let ids = [];
        ids.push(data?.id);
        deletes({
          ids: ids,
        }).then((res) => {
          toast.success(res.message);
          getTableData();
        });
      })
      .catch(() => {
        // 用户取消删除操作，不执行任何操作
      });
  };

  const isShowAction = {
    isShowEditor: "nt:nutag:template:update",
    isShowDetele: "nt:nutag:template:delete",
    isShowView: "nt:nutag:template:info",
  };

  const handlePageChange = useCallback(
    (pageIndex) => {
      setPagination((prev) => ({ ...prev, pageIndex }));
    },
    [setPagination]
  );

  const handlePageSizeChange = useCallback(
    (pageSize) => {
      setPagination({ pageIndex: 0, pageSize });
    },
    [setPagination]
  );

  const actionHandlers = useMemo(
    () => ({
      handlerView: (data) =>
        navigate("/view/template", {
          state: { id: data?.id, type: "view" },
        }),
      handlerEditor: (data) =>
        navigate("/add/template", {
          state: { id: data?.id, type: "editor" },
        }),

      Detele: (data) => {
        handlerDelete(data);
      },
    }),
    []
  );

  return (
    <ZktecoTable
      columns={columns}
      data={data}
      rowCount={rowCount}
      isLoading={isLoading}
      isRefetching={isRefetching}
      isError={isError}
      pathRoute="/add/template"
      loadDada={getTableData}
      paginationProps={{
        currentPage: pagination.pageIndex,
        rowsPerPage: pagination.pageSize,
        onPageChange: handlePageChange,
        onPageSizeChange: handlePageSizeChange,
      }}
      topActions={{
        showAdd:
          permission &&
          (permission.includes("nt:nutag:picture_library:save") ||
            permission.includes("*:*:*")),
      }}
      actionHandlers={actionHandlers}
      isShowAction={isShowAction}
    />
  );
}

export default TableList;
