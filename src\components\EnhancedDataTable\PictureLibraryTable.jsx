import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Chip,
  Avatar,
  Typography,
  IconButton,
  <PERSON>ltip,
  Stack,
  Card,
  CardContent,
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Download as DownloadIcon,
  Visibility as ViewIcon,
  CloudUpload as UploadIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import EnhancedDataTable from '../EnhancedDataTable';

const PictureLibraryTable = ({ 
  data = [], 
  loading = false, 
  onEdit, 
  onDelete, 
  onView, 
  onDownload,
  onRefresh,
  totalRecords = 0,
  page = 0,
  rowsPerPage = 10,
  onPageChange,
  onRowsPerPageChange,
}) => {
  const { t } = useTranslation();
  const [selectedRows, setSelectedRows] = useState([]);
  const [sortOrder, setSortOrder] = useState('asc');
  const [sortBy, setSortBy] = useState('');

  // 表格列配置
  const columns = [
    {
      field: 'name',
      headerName: t('picture_library.name'),
      minWidth: 200,
      sortable: true,
      renderCell: ({ value, row }) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
          <Avatar
            sx={{ 
              width: 36, 
              height: 36, 
              fontSize: '14px',
              backgroundColor: '#E3F2FD',
              color: '#1976D2',
            }}
            src={row.thumbnail}
          >
            {value?.charAt(0)?.toUpperCase()}
          </Avatar>
          <Box>
            <Typography 
              variant="body2" 
              sx={{ 
                fontWeight: 500,
                color: '#1a1a1a',
                maxWidth: 150,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
            >
              {value}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {row.uploadTime}
            </Typography>
          </Box>
        </Box>
      ),
    },
    {
      field: 'category',
      headerName: t('picture_library.category'),
      minWidth: 100,
      sortable: true,
      renderCell: ({ value }) => (
        <Chip
          label={value}
          size="small"
          sx={{
            backgroundColor: '#F0F7FF',
            color: '#1976D2',
            borderRadius: '6px',
            fontSize: '12px',
            fontWeight: 500,
          }}
        />
      ),
    },
    {
      field: 'client',
      headerName: t('picture_library.client'),
      minWidth: 120,
      sortable: true,
      renderCell: ({ value }) => (
        <Typography 
          variant="body2" 
          sx={{ 
            color: '#1976D2',
            fontWeight: 500,
            cursor: 'pointer',
            '&:hover': {
              textDecoration: 'underline',
            },
          }}
        >
          {value}
        </Typography>
      ),
    },
    {
      field: 'resolution',
      headerName: t('picture_library.resolution'),
      minWidth: 120,
      sortable: true,
      renderCell: ({ value }) => (
        <Chip
          label={value}
          size="small"
          variant="outlined"
          sx={{
            borderRadius: '6px',
            fontSize: '11px',
            backgroundColor: '#FAFAFA',
            borderColor: '#E0E0E0',
            color: '#666',
          }}
        />
      ),
    },
    {
      field: 'format',
      headerName: t('picture_library.format'),
      minWidth: 80,
      sortable: true,
      renderCell: ({ value }) => (
        <Box
          sx={{
            backgroundColor: getFormatColor(value).bg,
            color: getFormatColor(value).text,
            padding: '4px 8px',
            borderRadius: '4px',
            fontSize: '12px',
            fontWeight: 600,
            textTransform: 'uppercase',
            display: 'inline-block',
            minWidth: '40px',
            textAlign: 'center',
          }}
        >
          {value}
        </Box>
      ),
    },
    {
      field: 'size',
      headerName: t('picture_library.size'),
      minWidth: 100,
      align: 'right',
      sortable: true,
      renderCell: ({ value }) => (
        <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
          {value}
        </Typography>
      ),
    },
    {
      field: 'type',
      headerName: t('picture_library.type'),
      minWidth: 80,
      renderCell: ({ value }) => (
        value === '/' ? (
          <Typography variant="body2" color="text.disabled">
            -
          </Typography>
        ) : (
          <Chip
            label={value}
            size="small"
            color={value === '男' ? 'primary' : 'secondary'}
            sx={{ fontSize: '12px' }}
          />
        )
      ),
    },
    {
      field: 'status',
      headerName: t('common.status'),
      minWidth: 100,
      sortable: true,
      renderCell: ({ value }) => (
        <Chip
          label={getStatusLabel(value)}
          size="small"
          color={getStatusColor(value)}
          sx={{
            fontSize: '12px',
            fontWeight: 500,
            borderRadius: '12px',
          }}
        />
      ),
    },
    {
      field: 'actions',
      headerName: t('common.actions'),
      minWidth: 140,
      align: 'center',
      sortable: false,
      renderCell: ({ row }) => (
        <Stack direction="row" spacing={0.5}>
          <Tooltip title={t('common.view')}>
            <IconButton
              size="small"
              onClick={() => onView && onView(row)}
              sx={{
                color: '#52C41A',
                '&:hover': { backgroundColor: '#52C41A15' },
              }}
            >
              <ViewIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          <Tooltip title={t('common.edit')}>
            <IconButton
              size="small"
              onClick={() => onEdit && onEdit(row)}
              sx={{
                color: '#1890FF',
                '&:hover': { backgroundColor: '#1890FF15' },
              }}
            >
              <EditIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          <Tooltip title={t('common.download')}>
            <IconButton
              size="small"
              onClick={() => onDownload && onDownload(row)}
              sx={{
                color: '#722ED1',
                '&:hover': { backgroundColor: '#722ED115' },
              }}
            >
              <DownloadIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          <Tooltip title={t('common.delete')}>
            <IconButton
              size="small"
              onClick={() => onDelete && onDelete(row)}
              sx={{
                color: '#FF4D4F',
                '&:hover': { backgroundColor: '#FF4D4F15' },
              }}
            >
              <DeleteIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Stack>
      ),
    },
  ];

  // 获取格式颜色
  const getFormatColor = (format) => {
    const colors = {
      jpg: { bg: '#E6F7FF', text: '#1890FF' },
      jpeg: { bg: '#E6F7FF', text: '#1890FF' },
      png: { bg: '#F6FFED', text: '#52C41A' },
      gif: { bg: '#FFF7E6', text: '#FA8C16' },
      svg: { bg: '#F9F0FF', text: '#722ED1' },
      default: { bg: '#F5F5F5', text: '#666666' },
    };
    return colors[format?.toLowerCase()] || colors.default;
  };

  // 获取状态颜色
  const getStatusColor = (status) => {
    const statusMap = {
      active: 'success',
      inactive: 'default',
      pending: 'warning',
      error: 'error',
    };
    return statusMap[status] || 'default';
  };

  // 获取状态标签
  const getStatusLabel = (status) => {
    const labels = {
      active: t('common.active'),
      inactive: t('common.inactive'),
      pending: t('common.pending'),
      error: t('common.error'),
    };
    return labels[status] || status;
  };

  // 处理排序
  const handleSort = (field, order) => {
    setSortBy(field);
    setSortOrder(order);
    // 这里可以调用API进行服务端排序
    console.log('Sort by:', field, order);
  };

  // 处理选择变化
  const handleSelectionChange = (newSelection) => {
    setSelectedRows(newSelection);
  };

  // 处理行点击
  const handleRowClick = (row) => {
    onView && onView(row);
  };

  return (
    <Box>
      {/* 工具栏 */}
      <Card sx={{ mb: 2, boxShadow: 1 }}>
        <CardContent sx={{ py: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              {t('picture_library.title')}
            </Typography>
            <Stack direction="row" spacing={1}>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={onRefresh}
                size="small"
              >
                {t('common.refresh')}
              </Button>
              <Button
                variant="contained"
                startIcon={<UploadIcon />}
                size="small"
                sx={{
                  background: 'linear-gradient(45deg, #1976D2, #78BC27)',
                }}
              >
                {t('picture_library.upload')}
              </Button>
            </Stack>
          </Box>
          
          {selectedRows.length > 0 && (
            <Box sx={{ mt: 2, p: 1.5, backgroundColor: '#E3F2FD', borderRadius: 1 }}>
              <Typography variant="body2" color="primary">
                {t('common.selected_items', { count: selectedRows.length })}
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* 数据表格 */}
      <EnhancedDataTable
        columns={columns}
        rows={data}
        loading={loading}
        checkboxSelection={true}
        selectedRows={selectedRows}
        onSelectionChange={handleSelectionChange}
        onRowClick={handleRowClick}
        pagination={true}
        page={page}
        rowsPerPage={rowsPerPage}
        totalRecords={totalRecords}
        onPageChange={onPageChange}
        onRowsPerPageChange={onRowsPerPageChange}
        sortable={true}
        onSort={handleSort}
        orderBy={sortBy}
        order={sortOrder}
        dense={false}
        stickyHeader={true}
        maxHeight="calc(100vh - 300px)"
        showSkeleton={true}
        skeletonRows={rowsPerPage}
        animateRows={true}
        emptyMessage={t('picture_library.no_pictures')}
      />
    </Box>
  );
};

export default PictureLibraryTable;
