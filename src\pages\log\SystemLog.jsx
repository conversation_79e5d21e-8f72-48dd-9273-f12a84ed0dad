import React from "react";
import DataTable from "@c/DataTable";
import ListLayout from "@c/ListLayout";
import { getScreenLog } from "@s/screens";
import { getColums } from "./utils";
import { useTranslation } from "react-i18next";
function SystemLog() {
  const { t } = useTranslation();
  const [currentUrl, setCurrentUrl] = useState();
  const [open, setOpen] = useState(false);
  const [records, setRecords] = useState([]);
  const [totalRecords, setTotalRecords] = useState(0);
  const [filters, setFilters] = useState({
    pageNumber: 1,
    pageSize: 5,
  });

  const defaultFilters = {
    pageNumber: 1,
    pageSize: 5,
  };

  useEffect(() => {
    getScreenLog(filters).then((res) => {
      setRecords(res?.data?.data);
      setTotalRecords(res?.data?.total);
    });
  }, [filters]);

  const handlePageChange = (e) => {
    setFilters({ ...filters, pageNumber: e + 1 });
  };

  const handlePageSize = (e) => {
    setFilters({
      ...defaultFilters,
      pageNumber: defaultFilters.pageNumber,
      pageSize: e,
    });
  };

  const preView = (row) => {
    setCurrentUrl(row.url);
    setOpen(true);
  };

  return (
    <React.Fragment>
      <ListLayout
        title={t("synchronized.systemRecords")}
        toolbarProps={{
          refresh: true,
          onRefresh: (data) => {
            setFilters({ ...defaultFilters });
          },
        }}>
        <DataTable
          columns={getColums(preView)}
          rows={records}
          page={filters.pageNumber}
          totalRecords={totalRecords}
          getRowId={(row) => row.id}
          rowsPerPage={filters.pageSize}
          onPageChange={(pn) => handlePageChange(pn)}
          onPageSizeChange={(ps) => handlePageSize(ps)}
          onSelection={() => console.log("")}
        />
      </ListLayout>

      <PreViewImage
        open={open}
        setOpen={setOpen}
        currentUrl={currentUrl}></PreViewImage>
    </React.Fragment>
  );
}

export default SystemLog;

const PreViewImage = ({ open, setOpen, currentUrl }) => {
  return (
    <Dialog
      open={open}
      onClose={() => setOpen(false)}
      aria-describedby="alert-dialog-slide-description">
      <DialogContent>
        <img src={currentUrl}></img>
      </DialogContent>
    </Dialog>
  );
};
