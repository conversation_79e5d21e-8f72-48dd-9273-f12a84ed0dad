import React, { useState, useMemo, useCallback } from 'react';
import { Box, Typography, Grid, Tooltip } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { toast } from 'react-toastify';
import ZktecoTable from './index';

// 导入图标
import FilterListIcon from '@mui/icons-material/FilterList';
import SearchIcon from '@mui/icons-material/Search';
import SettingsIcon from '@mui/icons-material/Settings';
import ExportIcon from '@mui/icons-material/GetApp';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import VisibilityIcon from '@mui/icons-material/Visibility';
import StarIcon from '@mui/icons-material/Star';

const TableTestExample = () => {
  const { t } = useTranslation();
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });

  // 模拟数据 - 包含很多列来测试横向滚动
  const mockData = useMemo(() => [
    {
      id: 1,
      screenId: 'SCR001',
      screenName: '主屏幕显示器',
      resolution: '1920x1080',
      positionNO: 'A001',
      powerStatus: '正常',
      deviceSn: 'DEV001',
      outletName: '北京店',
      productId: 'PROD001',
      templateId: 'TEMP001',
      screenStatus: '在线',
      screenUpdateTime: '2024-01-15 10:30:00',
      battery: 85,
      status: '1',
      isFree: 0,
      subStatus: 1,
      // 额外的列来测试横向滚动
      column1: '数据1',
      column2: '数据2',
      column3: '数据3',
      column4: '数据4',
      column5: '数据5',
      column6: '数据6',
      column7: '数据7',
      column8: '数据8',
    },
    {
      id: 2,
      screenId: 'SCR002',
      screenName: '副屏幕显示器',
      resolution: '1366x768',
      positionNO: 'A002',
      powerStatus: '低电量',
      deviceSn: 'DEV002',
      outletName: '上海店',
      productId: 'PROD002',
      templateId: 'TEMP002',
      screenStatus: '离线',
      screenUpdateTime: '2024-01-15 09:15:00',
      battery: 25,
      status: '0',
      isFree: 1,
      subStatus: 2,
      column1: '数据A',
      column2: '数据B',
      column3: '数据C',
      column4: '数据D',
      column5: '数据E',
      column6: '数据F',
      column7: '数据G',
      column8: '数据H',
    },
    // 更多数据...
  ], []);

  // 表格列配置 - 包含很多列来测试横向滚动
  const columns = useMemo(() => [
    {
      accessorKey: 'id',
      header: 'ID',
      enableColumnActions: false,
      enableSorting: false,
      size: 80,
    },
    {
      accessorKey: 'screenId',
      header: '屏幕ID',
      enableColumnActions: false,
      enableSorting: false,
      size: 120,
    },
    {
      accessorKey: 'screenName',
      header: '屏幕名称',
      enableColumnActions: false,
      enableSorting: false,
      size: 150,
    },
    {
      accessorKey: 'resolution',
      header: '分辨率',
      enableColumnActions: false,
      enableSorting: false,
      size: 120,
    },
    {
      accessorKey: 'positionNO',
      header: '位置编号',
      enableColumnActions: false,
      enableSorting: false,
      size: 120,
    },
    {
      accessorKey: 'powerStatus',
      header: '电源状态',
      enableColumnActions: false,
      enableSorting: false,
      size: 120,
    },
    {
      accessorKey: 'deviceSn',
      header: '设备序列号',
      enableColumnActions: false,
      enableSorting: false,
      size: 150,
    },
    {
      accessorKey: 'outletName',
      header: '门店名称',
      enableColumnActions: false,
      enableSorting: false,
      size: 120,
    },
    {
      accessorKey: 'productId',
      header: '产品ID',
      enableColumnActions: false,
      enableSorting: false,
      size: 120,
    },
    {
      accessorKey: 'templateId',
      header: '模板ID',
      enableColumnActions: false,
      enableSorting: false,
      size: 120,
    },
    {
      accessorKey: 'screenStatus',
      header: '屏幕状态',
      enableColumnActions: false,
      enableSorting: false,
      size: 120,
      Cell: ({ row }) => (
        <span style={{ color: row.original.status === '1' ? 'green' : 'red' }}>
          {row.original.screenStatus}
        </span>
      ),
    },
    {
      accessorKey: 'screenUpdateTime',
      header: '更新时间',
      enableColumnActions: false,
      enableSorting: false,
      size: 180,
    },
    // 额外的列来测试横向滚动
    {
      accessorKey: 'column1',
      header: '扩展列1',
      enableColumnActions: false,
      enableSorting: false,
      size: 100,
    },
    {
      accessorKey: 'column2',
      header: '扩展列2',
      enableColumnActions: false,
      enableSorting: false,
      size: 100,
    },
    {
      accessorKey: 'column3',
      header: '扩展列3',
      enableColumnActions: false,
      enableSorting: false,
      size: 100,
    },
    {
      accessorKey: 'column4',
      header: '扩展列4',
      enableColumnActions: false,
      enableSorting: false,
      size: 100,
    },
    {
      accessorKey: 'column5',
      header: '扩展列5',
      enableColumnActions: false,
      enableSorting: false,
      size: 100,
    },
  ], []);

  // 自定义顶部操作按钮
  const customTopActions = useMemo(() => [
    {
      icon: <FilterListIcon />,
      title: '筛选',
      tooltip: '筛选数据',
      onClick: () => {
        console.log('筛选操作');
        toast.info('筛选功能已触发');
      },
      color: '#1976d2',
    },
    {
      icon: <SearchIcon />,
      title: '搜索',
      tooltip: '搜索数据',
      onClick: () => {
        console.log('搜索操作');
        toast.info('搜索功能已触发');
      },
      color: '#2e7d32',
    },
    {
      icon: <SettingsIcon />,
      title: '设置',
      tooltip: '表格设置',
      onClick: () => {
        console.log('设置操作');
        toast.info('设置功能已触发');
      },
      color: '#ed6c02',
    },
    {
      icon: <ExportIcon />,
      title: '导出',
      tooltip: '导出数据',
      onClick: () => {
        console.log('导出操作');
        toast.info('导出功能已触发');
      },
      color: '#9c27b0',
    },
  ], []);

  // 自定义行操作渲染
  const renderCustomRowActions = useCallback(({ row }) => {
    return (
      <Grid container spacing={1} sx={{ display: 'flex', flexDirection: 'row' }}>
        <Grid item>
          <Tooltip title="查看详情" arrow placement="bottom">
            <VisibilityIcon
              sx={{
                cursor: 'pointer',
                color: '#1976d2',
                fontSize: '18px',
                '&:hover': { color: '#1565c0', transform: 'scale(1.1)' },
                transition: 'all 0.2s ease',
              }}
              onClick={() => {
                console.log('查看详情', row.original);
                toast.info(`查看 ${row.original.screenName} 详情`);
              }}
            />
          </Tooltip>
        </Grid>
        
        <Grid item>
          <Tooltip title="编辑" arrow placement="bottom">
            <EditIcon
              sx={{
                cursor: 'pointer',
                color: '#2e7d32',
                fontSize: '18px',
                '&:hover': { color: '#1b5e20', transform: 'scale(1.1)' },
                transition: 'all 0.2s ease',
              }}
              onClick={() => {
                console.log('编辑', row.original);
                toast.info(`编辑 ${row.original.screenName}`);
              }}
            />
          </Tooltip>
        </Grid>
        
        <Grid item>
          <Tooltip title="删除" arrow placement="bottom">
            <DeleteIcon
              sx={{
                cursor: 'pointer',
                color: '#d32f2f',
                fontSize: '18px',
                '&:hover': { color: '#c62828', transform: 'scale(1.1)' },
                transition: 'all 0.2s ease',
              }}
              onClick={() => {
                console.log('删除', row.original);
                if (window.confirm(`确定要删除 ${row.original.screenName} 吗？`)) {
                  toast.success('删除成功');
                }
              }}
            />
          </Tooltip>
        </Grid>

        {/* 条件显示的收藏按钮 */}
        {row.original.status === '1' && (
          <Grid item>
            <Tooltip title="收藏" arrow placement="bottom">
              <StarIcon
                sx={{
                  cursor: 'pointer',
                  color: '#ff9800',
                  fontSize: '18px',
                  '&:hover': { color: '#f57c00', transform: 'scale(1.1)' },
                  transition: 'all 0.2s ease',
                }}
                onClick={() => {
                  console.log('收藏', row.original);
                  toast.success(`已收藏 ${row.original.screenName}`);
                }}
              />
            </Tooltip>
          </Grid>
        )}
      </Grid>
    );
  }, []);

  const handlePageChange = useCallback((pageIndex) => {
    setPagination(prev => ({ ...prev, pageIndex }));
  }, []);

  const handlePageSizeChange = useCallback((pageSize) => {
    setPagination({ pageIndex: 0, pageSize });
  }, []);

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" sx={{ mb: 3, fontWeight: 600 }}>
        ZktecoTable 增强功能测试
      </Typography>
      
      <Typography variant="body1" sx={{ mb: 2, color: 'text.secondary' }}>
        这个示例展示了：
      </Typography>
      <ul style={{ marginBottom: '24px', color: '#666' }}>
        <li>✅ 横向滚动条功能（表格列很多时会出现）</li>
        <li>✅ 右上角 4 个自定义图标</li>
        <li>✅ 操作列 3-4 个自定义图标</li>
        <li>✅ 图标悬停效果和颜色定制</li>
        <li>✅ 条件显示逻辑</li>
      </ul>

      <ZktecoTable
        columns={columns}
        data={mockData}
        rowCount={mockData.length}
        headerTitle="屏幕管理测试表格"
        isLoading={false}
        isError={false}
        isRefetching={false}
        paginationProps={{
          currentPage: pagination.pageIndex,
          rowsPerPage: pagination.pageSize,
          onPageChange: handlePageChange,
          onPageSizeChange: handlePageSizeChange,
        }}
        topActions={{
          showAdd: true,
          showRefresh: true,
          showDownload: false,
          showUpload: false,
          customActions: customTopActions,
          onAdd: () => toast.info('添加功能'),
          onRefresh: () => toast.success('刷新成功'),
        }}
        getRowId={(originalRow) => originalRow?.id}
        renderRowActions={renderCustomRowActions}
        actionHandlers={{
          handlerEditor: (data) => {
            console.log('编辑处理器', data);
            toast.info(`编辑 ${data.screenName}`);
          },
        }}
        isShowAction={{
          isShowEditor: true,
        }}
        enablePagination={true}
        manualPagination={true}
        enableRowActions={true}
        actionColumnWidth={200} // 增加操作列宽度以容纳更多图标
      />
    </Box>
  );
};

export default TableTestExample;
