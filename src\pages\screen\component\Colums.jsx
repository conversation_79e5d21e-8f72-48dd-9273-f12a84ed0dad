import IconHandaler from "@c/IconHandaler";
import EditIcon from "@mui/icons-material/Edit";
import ClearIcon from "@mui/icons-material/Clear";
import SearchIcon from "@mui/icons-material/Search";
import BindIcon from "@a/images/bind_icon.svg?react";
import {
  calculateBatteryStatus,
  getFormattedDate,
  handleDataBind,
} from "./utils";
import CommonUtil from "@/util/CommonUtils.js";
import AuthButton from "@/components/AuthButton.jsx";
export const getColums = (
  t,
  setPreViewOpen,
  setPreViewItem,
  navigate,
  records
) => {
  let columns = [
    {
      field: "id",
      headerName: t("screen.screenId"),
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (e) => e.row.id,
    },
    {
      field: "resolution",
      headerName: t("screen.screen_resolution"),
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (e) => e.row.resolution,
    },
    {
      field: "positionNO",
      headerName: t("screen.positionNo"),
      flex: 1.2,
      headerAlign: "center",
      align: "center",
      renderCell: (e) => e.row.positionNo,
    },
    {
      field: "screenName",
      headerName: t("screen.screenName"),
      flex: 1.2,
      headerAlign: "center",
      align: "center",
      renderCell: (e) => e.row.screenName,
    },
    {
      field: "powerStatus",
      headerName: t("screen.powerStatus"),
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (e) => calculateBatteryStatus(e.row.battery),
    },
    {
      field: "gateway",
      headerName: t("screen.gateway"),
      flex: 1.5,
      headerAlign: "center",
      align: "center",
      renderCell: (e) => e.row.sn,
    },
    {
      field: "outletName",
      headerName: t("screen.outlet"),
      flex: 1.2,
      headerAlign: "center",
      align: "center",
      renderCell: (e) => e.row.outletName,
    },
    {
      field: "productId",
      headerName: t("screen.bindToProduct"),
      flex: 1.2,
      headerAlign: "center",
      align: "center",
      renderCell: (e) => e.row.productName,
    },
    {
      field: "templateId",
      headerName: t("screen.templatePreview"),
      flex: 1.2,
      headerAlign: "center",
      align: "center",
      renderCell: (e) => {
        return (
          <Tooltip title={t("common.preview")} arrow>
            <Button
              id="temppreview"
              sx={{ px: 0 }}
              onClick={() => {
                let templateJson = e.row.templateJson;

                if (templateJson) {
                  setPreViewOpen(true);
                  setPreViewItem(e.row);
                } else {
                  // getPicPreview(e);
                  toast.info(t("screen.unbound_templates"));
                }
              }}>
              {t("common.preview")}
            </Button>
          </Tooltip>
        );
      },
    },

    {
      field: "screenStatus",
      headerName: t("screen.screenStatus"),
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (e) => (
        <Tooltip
          title={e.row.status ? t("menu.online") : t("menu.offline")}
          arrow
          placement="bottom">
          <span style={{ color: e.row.status === "1" ? "green" : "red" }}>
            {e.row.status ? t("menu.online") : t("menu.offline")}
          </span>
        </Tooltip>
      ),
    },
    {
      field: "screenUpdateTime",
      headerName: t("screen.screenUpdateTime"),
      flex: 1.5,
      headerAlign: "center",
      align: "center",
      renderCell: (e) => (
        <>
          <Tooltip
            title={getFormattedDate(e.row.updatedAt)}
            arrow
            placement="bottom">
            <span>
              {CommonUtil.formatLongText(getFormattedDate(e.row.updatedAt))}
            </span>
          </Tooltip>
        </>
      ),
    },
    {
      headerName: t("common.actions"),
      sortable: false,
      headerAlign: "center",
      align: "center",
      flex: 1,
      renderCell: (e) => (
        <IconHandaler>
          <AuthButton button="nt:nutag:screen:update">
            <Tooltip title={t("common.edit")} arrow sx={{ marginLeft: 0.5 }}>
              <EditIcon
                style={{
                  alignSelf: "center",
                  paddingTop: "0px",
                  cursor: "pointer",
                  opacity: "0.6",
                  height: "17px",
                  width: "20px",
                }}
                onClick={() => {
                  const id = e.row.id;
                  const deviceId = e.row.deviceId;
                  navigate("/edit/screens", {
                    state: { id: id, deviceId: deviceId },
                  });
                }}
              />
            </Tooltip>
          </AuthButton>
          <AuthButton button="nt:nutag:screen:send">
            <Tooltip
              title={t("screen.bindData")}
              arrow
              sx={{ marginLeft: "10px" }}>
              <BindIcon
                style={{
                  alignSelf: "center",
                  paddingTop: "0px",
                  cursor: "pointer",
                  opacity: "0.6",
                  height: "17px",
                  width: "20px",
                }}
                onClick={() => {
                  calculateBatteryStatus(e.row.battery);
                  if (calculateBatteryStatus(e.row.battery) == "0%") {
                    toast.error("Screen battery is 0%");
                    return;
                  }
                  handleDataBind(e.row.id, records, navigate);
                }}
              />
            </Tooltip>
          </AuthButton>
        </IconHandaler>
      ),
    },
  ];

  return columns;
};

export const searchScreenProps = (
  searchValue,
  setSearchValue,
  t,
  filters,
  setFilters
) => {
  return (
    <TextField
      label={t("tips_screens.search_by_screenId")}
      value={searchValue}
      onKeyDown={handleKeyDown} // 添加 onKeyDown 事件
      onChange={(e) => setSearchValue(e.target.value)}
      InputProps={{
        style: {
          height: 50,
          width: "300px",
          background: "#fff",
          color: "#474B4F",
          opacity: "0.6",
          boxShadow: "0px 1px 3px #0000001A",
          borderRadius: "8px",
          border: "0px !important",
          padding: "10px",
        },
        endAdornment: (
          <InputAdornment position="end">
            <IconButton
              id="clearButton"
              onClick={handlerClear}
              style={{ visibility: searchValue ? "visible" : "hidden" }} // 根据输入框内容控制可见性
            >
              <ClearIcon />
            </IconButton>
            <IconButton
              id="ScreenBtn1"
              onClick={() =>
                handleSearchClick(searchValue, filters, setFilters)
              }>
              <SearchIcon />
            </IconButton>
          </InputAdornment>
        ),
      }}
      sx={{
        "& .MuiInputLabel-root": {
          color: "#D1D1D1",
          cursor: "pointer",
        },
        cursor: "pointer",
      }}
    />
  );
};

const handleSearchClick = (filters, searchValue, setFilters) => {
  setFilters({
    ...filters,
    id: searchValue,
    operator: "AND",
  });
};

const handleKeyDown = (event) => {
  if (event.key === "Enter") {
    handleSearchClick(filters, searchValue, setFilters); // 调用搜索函数
  }
};

const handlerClear = () => {
  setSearchValue("");
  setFilters({
    page: 1,
    pageSize: 5,
    name: "",
  });
};
