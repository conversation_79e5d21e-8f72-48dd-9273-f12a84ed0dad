import { Grid } from "@mui/material";
import OutletInput from "../Schedule/OutletInput";

// 变价名称模块
export const getPriceNameConfig = (t) => {
  let baseConfig = [
    {
      name: "name",
      label: t("events.priceChangeName"),
      type: "input",
      // placeholder: t("Please enter the name of the price change"),
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("tips.required"),
        },
      ],
    },
  ];

  return baseConfig;
};

// 变价名称模块
export const getOutletConfig = (t, deviceOnlineOutlet, addFormik) => {
  let baseConfig = [
    {
      name: "outletIds",
      label: t("events.outlet"),
      placeholder: t("请选择门店"),
      options: deviceOnlineOutlet,
      custom: true,
      renderingCustomItem: () => {
        return (
          <Grid xs={3.9} ml={2}>
            <OutletInput
              required
              label={t("events.outlet")}
              addFormik={addFormik}></OutletInput>
          </Grid>
        );
      },
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("请选择门店"),
        },
      ],
    },
  ];

  return baseConfig;
};

// 商品模块配置数据
export const getProductConfig = (
  t,
  productLabel,
  brandData,
  productData,
  selectLabel
) => {
  let baseConfig = [
    {
      name: "productAttributeId",
      label: t("product.title"),
      type: "auto",
      required: true,
      options: productLabel,
      typeValue: "4",
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("tips.required"),
        },
      ],
    },
    {
      name: "productAttributeValue",
      label: selectLabel ? selectLabel : " ",
      type: "auto",
      // placeholder: t("请选择品牌"),
      required: true,
      typeValue: "3",
      isValue: "true",
      options: brandData,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("tips.required"),
        },
      ],
    },
    {
      name: "productId",
      label: t("table_product.product_name"),
      type: "auto",
      // placeholder: t("EVSC0002"),
      required: true,
      typeValue: "5",
      options: productData,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("tips.required"),
        },
      ],
    },
  ];

  return baseConfig;
};

// 商品模块配置数据
export const getPriceRuleConfig = (
  t,
  addFormik,
  labelValue,
  isShowPrecent,
  inputRef
) => {
  const promotionType = [
    { id: "1", value: t("dictionary.discount") },
    { id: "2", value: t("dictionary.byunit") },
    { id: "3", value: t("dictionary.by_value") },
    { id: "4", value: t("dictionary.promotion") },
  ];
  let baseConfig = [
    {
      name: "promotionType",
      label: t("events.promotionType"),
      type: "auto",
      // placeholder: t("请选择促销类型"),
      typeValue: "3",
      required: true,
      options: promotionType,
      handleChange: () => {
        inputRef.current?.focus();
      },
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("tips.required"),
        },
      ],
    },
    {
      name: "promotionValue",
      label: labelValue ? labelValue : " ",
      type: "input",
      // placeholder: t("Please enter Discount"),
      required: true,
      inputType: isShowPrecent,
      inputRef: inputRef,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t(
            labelValue
              ? `${labelValue} ` + t("tips.required_field")
              : t("price.promotiontype")
          ),
        },

        {
          type:
            isShowPrecent == "1" || labelValue == "Discount"
              ? "number"
              : "null",
          message: t("price.number"),
        },
        {
          type:
            isShowPrecent == "1" || labelValue == "Discount"
              ? "discount"
              : "null",
          message: t("price.maximum_discount"),
        },
      ],
    },
  ];

  return baseConfig;
};

//模板选择模块配置数据
export const getTemplateConfig = (t, addFormik, templateList) => {
  let baseConfig = [
    {
      name: "templateId",
      label: t("events.promotionType"),
      custom: true,
      sx: 12,
      renderingCustomItem: (item) => {},
    },
  ];

  return baseConfig;
};

// 变价计划模块配置数据
export const getScheduleConfig = (t, does, timeZoneList) => {
  const scheduleModes = [
    { id: 2, value: t("enum.does_not_repeat") },
    { id: 3, value: t("enum.daily") },
    { id: 4, value: t("enum.weekly") },
    { id: 5, value: t("enum.monthly") },
    { id: 6, value: t("enum.annually") },
  ];

  let baseConfig = [
    {
      name: "scheduleMode",
      label: t("events.scheduleMode"),
      type: "auto",
      placeholder: t("tips.select_price_period"),
      sx: 4,
      options: scheduleModes,
    },
    {
      name: "timeZone",
      label: t("events.timeZone"),
      type: "auto",
      disabled: true,
      isValue: "zoon",
      typeValue: "5",
      sx: 4,
      options: timeZoneList,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("price.select_timezone"),
        },
      ],
    },

    {
      type: "null",
      sx: 6,
      custom: true,
      renderingCustomItem: () => {
        return <Grid width={"30%"}></Grid>;
      },
    },
    {
      name: "startTime",
      label: t("events.startPromotion"),
      required: true,
      placeholder: t("common_tips.start_date"),
      type: "date",
      sx: 2,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("common_tips.start_date"),
        },
      ],
    },
    {
      name: "timeDay",
      placeholder: t("price.select_time"),
      type: "time",
      sx: 2,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("price.select_starttime"),
        },
      ],
    },

    {
      name: "endTime",
      label: t("events.endPromotion"),
      placeholder: t("common_tips.end_date"),
      type: "date",
      minDate: new Date(),
      sx: 2,
      conditionalRendering: (value) => {
        return does ? false : true;
      },
    },
    {
      name: "endTimeDay",
      placeholder: t("common_tips.end_date"),
      type: "time",
      conditionalRendering: (value) => {
        return does ? false : true;
      },
      sx: 2,
    },
  ];

  return baseConfig;
};
