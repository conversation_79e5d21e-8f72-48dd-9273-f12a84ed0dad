/**
 * 微前端导航测试工具
 * 用于验证导航功能是否正常工作
 */

import { safeMicroFrontendNavigate, navigateWithToast } from './microFrontendNavigation';

/**
 * 测试微前端导航功能
 * @param {Function} navigate - React Router的navigate函数
 * @param {Function} toast - toast函数
 */
export const testMicroFrontendNavigation = (navigate, toast) => {
  console.log('=== 微前端导航测试开始 ===');
  
  // 测试1: 检测环境
  const isMicroFrontend = !!(window.__POWERED_BY_QIANKUN__ || window.qiankunProps);
  console.log('1. 微前端环境检测:', isMicroFrontend ? '是' : '否');
  
  // 测试2: 基础导航
  console.log('2. 测试基础导航功能...');
  try {
    safeMicroFrontendNavigate(navigate, '/test-path');
    console.log('✅ 基础导航测试通过');
  } catch (error) {
    console.error('❌ 基础导航测试失败:', error);
  }
  
  // 测试3: 带Toast的导航
  console.log('3. 测试带Toast的导航功能...');
  try {
    navigateWithToast(navigate, '/test-path', toast, '测试消息');
    console.log('✅ 带Toast导航测试通过');
  } catch (error) {
    console.error('❌ 带Toast导航测试失败:', error);
  }
  
  console.log('=== 微前端导航测试完成 ===');
};

/**
 * 监听导航变化
 * @param {Object} location - React Router的location对象
 */
export const logNavigationChange = (location) => {
  console.log('🔄 路由变化:', {
    pathname: location.pathname,
    search: location.search,
    hash: location.hash,
    state: location.state,
    timestamp: new Date().toISOString()
  });
};

/**
 * 检查页面是否重新加载
 */
export const checkPageReload = () => {
  const startTime = performance.now();
  
  window.addEventListener('beforeunload', () => {
    console.warn('⚠️ 页面即将重新加载 - 这可能表示导航有问题');
  });
  
  window.addEventListener('load', () => {
    const loadTime = performance.now() - startTime;
    if (loadTime > 100) {
      console.warn('⚠️ 页面加载时间较长:', loadTime + 'ms - 可能发生了重新加载');
    } else {
      console.log('✅ 页面加载正常:', loadTime + 'ms');
    }
  });
};
