import React from "react";
import CommonUtil from "@u/CommonUtils";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate } from "react-router-dom";
import RightViewLayout from "@c/RighViewLayout";
import CustomInput from "@c/CustomInput";
import { getDetail, edit } from "@/services/screens.js";
import { calculateBatteryStatus } from "./utils";
import { toast } from "react-toastify";
function editScreen() {
  let navigate = useNavigate();
  const { t } = useTranslation();
  const location = useLocation();
  const [screenId, setScreenId] = useState("");
  const [gatewayDevice, setGatewayDevice] = useState(null);
  const [bindGatway, setBindGatway] = useState(null);

  const bindGateway = [
    {
      id: "0",
      value: t("common.is_not"),
    },
    {
      id: "1",
      value: t("common.is"),
    },
  ];
  const [payload, setPayload] = useState({
    sn: "",
    battery: "",
    screenId: "",
    screenName: "",
    positionNo: "",
  });

  const [error, setError] = useState({
    sn: "",
    battery: "",
    screenId: "",
    screenName: "",
    positionNo: "",
  });

  useEffect(() => {
    const id = location?.state?.id;
    getDetail(id).then((response) => {
      setScreenId(response.data.id);
      setGatewayDevice(response.data.bindDevice);
      setPayload((prevPayload) => ({
        ...prevPayload,
        screenId: response.data.id,
        sn: response.data.deviceSn,
        battery: response.data.battery,
        screenName: response.data.screenName,
        positionNo: response.data.positionNo,
        bindDevice: response.data.bindDevice,
      }));
    });
  }, []);

  useEffect(() => {
    setBindGatway(bindGateway.find((item) => item.id == gatewayDevice));
  }, [gatewayDevice]);

  const handleChange = (event) => {
    const name = event.target.name;
    setPayload({
      ...payload,
      [name]: event.target.value,
    });

    setError({
      ...error,
      [name]: "",
      common: "",
    });
  };

  const validateForm = () => {
    if (CommonUtil.isEmptyString(payload.sn)) {
      setError({
        ...error,
        sn: t("tips.required"),
      });
      return;
    }

    return true;
  };

  const handleSubmit = () => {
    if (validateForm()) {
      var request = {
        ...payload,
        id: location?.state?.id,
        deviceSn: payload.sn,
        bindDevice: payload.bindDevice?.id,
      };
      edit(location.state?.id, request).then((res) => {
        navigate("/screens");
        toast.success(res.message);
      });
    }
  };
  return (
    <React.Fragment>
      <RightViewLayout
        navigateBack={"/screens"}
        title={t("screen.edit_screen")}>
        <Grid container spacing={2} px={2}>
          <Grid item md={6} xs={12}>
            <CustomInput
              id="EditScreen1"
              required
              label={t("screen.screenId")}
              size="small"
              name="screenId"
              value={payload.screenId}
              inputProps={{
                maxLength: 60,
              }}
              helperText={error.name}
              validation={"alpha-numeric"}
              disabled={true}
            />
          </Grid>
          <Grid item md={6} xs={12}>
            <CustomInput
              id="EditScreen2"
              label={t("screen.positionNo")}
              size="small"
              name="positionNo"
              error={error.positionNo}
              resetError={() => setError({ ...error, positionNo: "" })}
              value={payload.positionNo}
              handleChange={handleChange}
              inputProps={{
                maxLength: 5,
              }}
              placeholder={t("tips_screens.enter_position_no")}
              helperText={error.positionNo}
              validation={"alpha-numeric"}
              // disabled={true}
            />
          </Grid>
          <Grid item md={6} xs={12}>
            <CustomInput
              id="EditScreen3"
              label={t("screen.screenName")}
              size="small"
              name="screenName"
              error={error.screenName}
              resetError={() => setError({ ...error, screenName: "" })}
              value={payload.screenName}
              handleChange={handleChange}
              inputProps={{
                maxLength: 10,
              }}
              placeholder={t("tips_screens.enter_screen_name")}
              helperText={error.screenName}
              validation={"alpha-numeric"}
              // disabled={true}
            />
          </Grid>
          <Grid item md={6} xs={12}>
            <CustomInput
              id="EditScreen4"
              required
              label={t("screen.powerStatus")}
              size="small"
              name="battery"
              value={calculateBatteryStatus(payload.battery)}
              inputProps={{
                maxLength: 60,
              }}
              helperText={error.name}
              validation={"alpha-numeric"}
              disabled={true}
            />
          </Grid>
          <Grid item md={6} xs={12}>
            <CustomInput
              id="EditScreen5"
              required
              label={t("screen.gateway")}
              size="small"
              name="sn"
              error={error.sn}
              resetError={() => setError({ ...error, sn: "" })}
              value={payload.sn}
              handleChange={handleChange}
              inputProps={{
                maxLength: 60,
              }}
              helperText={error.sn}
              placeholder={t("tips_screens.enter_gateway_sn")}
              validation={"alpha-numeric"}
              // disabled={true}
            />
          </Grid>

          <Grid item md={6} xs={12}>
            <InputLabel
              shrink
              htmlFor="bootstrap-input"
              style={{ paddingLeft: "0px" }}>
              {t("template.is_bind_device")}
              <span style={{ color: "red" }}>*</span>
            </InputLabel>

            <Autocomplete
              id="AddTemplateModel"
              noOptionsText={t("tips.no_options")}
              options={bindGateway}
              value={bindGatway}
              getOptionLabel={(option) => (option?.value ? option?.value : "")}
              onChange={(e, v) => {
                setBindGatway(v);
                setPayload({
                  ...payload,
                  bindDevice: v,
                });
                setError({
                  ...error,
                  bindDevice: `${t("")}`,
                });
              }}
              disableClearable={true} // 禁用叉号清空按钮
              renderInput={(params) => (
                <TextField
                  {...params}
                  name="bindDevice"
                  size="small"
                  error={error.bindDevice}
                  helperText={error.bindDevice}
                  sx={{
                    "& .MuiOutlinedInput-input.MuiInputBase-inputSizeSmall": {
                      fontSize: "13px",
                      padding: "12px",
                      height: "28px",
                      borderRadius: "18px",
                    },
                  }}
                />
              )}
            />
          </Grid>
        </Grid>
        <Grid container spacing={2} px={2} mt={3}>
          <Grid item xs={12}>
            <Box display={"flex"} flexDirection={"row-reverse"}>
              <Box item pl={2}>
                <Button
                  id="EditScreen-button-01"
                  variant="contained"
                  sx={{
                    marginLeft: 1,
                    background: `linear-gradient(to right, ${"#1487CA"}, ${"#78BC27"})`,
                    padding: "4px",
                  }}
                  size="large"
                  className="text-transform-none"
                  onClick={handleSubmit}>
                  {t("common.save")}
                </Button>
              </Box>
              <Box item>
                <Button
                  id="EditScreen-button-02"
                  className="text-transform-none"
                  variant="outlined"
                  onClick={() => navigate("/screens")}
                  size="large">
                  {t("common.back")}
                </Button>
              </Box>
            </Box>
          </Grid>
        </Grid>
      </RightViewLayout>
    </React.Fragment>
  );
}

export default editScreen;
