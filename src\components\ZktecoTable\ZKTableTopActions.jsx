import React from "react";
import { Grid, Typography, <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "@mui/material";
import AuthButton from "../AuthButton";
import { useTranslation } from "react-i18next";
import DownLoadIcon from "@/assets/Icons/DownloadIcon.svg?react";
import UploadIcon from "@/assets/Icons/UploadIcon.svg?react";
import RefreshIcon from "@/assets/Icons/RefreshIcon.svg?react";
import AddIcon from "@/assets/Icons/Plus icon.svg?react";

const ZKTableTopActions = ({
  headerTitle,
  showDownload,
  showUpload,
  showRefresh,
  showAdd,
  downCallBack,
  uploadCallBack,
  refreshCallback,
  addCallback,
  // 新增自定义图标支持
  customActions = [],
}) => {
  const { t } = useTranslation();

  const buttons = [
    {
      show: showDownload,
      cb: downCallBack,
      icon: <DownLoadIcon />,
      title: "DownLoad",
    },
    {
      show: showUpload,
      cb: uploadCallBack,
      icon: <UploadIcon />,
      title: "Upload",
    },
    {
      show: showRefresh,
      cb: refreshCallback,
      icon: <RefreshIcon />,
      title: "Refresh",
    },
    { show: showAdd, cb: addCallback, icon: <AddIcon />, title: "Add" },
  ];

  return (
    <Grid
      sx={{
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
      }}>
      <Typography pl={4} variant="h5">
        {headerTitle}
      </Typography>
      <Grid sx={{ display: "flex", gap: 1, alignItems: "center" }}>
        {/* 默认按钮 */}
        {buttons.map((btn, i) => (
          <AuthButton key={i} button={btn.show}>
            <Tooltip title={t(btn.title)}>
              <Button onClick={btn.cb}>{btn.icon}</Button>
            </Tooltip>
          </AuthButton>
        ))}

        {/* 自定义按钮 */}
        {customActions.map((action, index) => (
          <Tooltip
            key={`custom-${index}`}
            title={action.tooltip || action.title}>
            <Button
              onClick={action.onClick}
              disabled={action.disabled}
              sx={{
                minWidth: "40px",
                padding: "8px",
                color: action.color || "inherit",
                backgroundColor: action.backgroundColor || "transparent",
                "&:hover": {
                  backgroundColor: action.hoverColor || "rgba(0, 0, 0, 0.04)",
                },
                ...action.sx,
              }}>
              {action.icon}
            </Button>
          </Tooltip>
        ))}
      </Grid>
    </Grid>
  );
};

export default ZKTableTopActions;
