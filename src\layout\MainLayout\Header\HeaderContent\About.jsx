import React, { useRef, useState } from "react";
// material-ui
import { useTheme } from "@mui/material/styles";
import {
  Box,
  Stack,
  Grid,
  IconButton,
  Typography,
  useMediaQuery,
} from "@mui/material";
import {
  BootstrapDialog,
  BootstrapDialogTitle,
  BootstrapActions,
  BootstrapContent,
} from "@/components/dialog";
// lang
import { useTranslation } from "react-i18next";
import enLogo from "@/assets/logo/about_logo_en.svg";
import zhLogo from "@/assets/logo/about_logo_zh.svg";

// assets
import { InfoCircleOutlined } from "@ant-design/icons";
import { getStoreLang, setStoreLang } from "@/util/langUtils";

const About = () => {
  const { i18n } = useTranslation();
  const theme = useTheme();
  const matchesXs = useMediaQuery(theme.breakpoints.down("md"));

  const anchorRef = useRef(null);
  const [open, setOpen] = useState(false);
  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen);
  };

  const handleClose = (event) => {
    if (anchorRef.current && anchorRef.current.contains(event.target)) {
      return;
    }
    setOpen(false);
  };

  const iconBackColorOpen = "grey.300";
  const iconBackColor = "grey.100";

  return (
    <Box sx={{ flexShrink: 0, ml: 0.75, mr: 3 }}>
      <IconButton
        disableRipple
        color="secondary"
        sx={{
          color: "text.primary",
          bgcolor: open ? iconBackColorOpen : iconBackColor,
        }}
        aria-label="open profile"
        ref={anchorRef}
        aria-controls={open ? "profile-grow" : undefined}
        aria-haspopup="true"
        onClick={handleToggle}>
        <InfoCircleOutlined />
      </IconButton>
      <AboutContent open={open} onClose={handleClose} />
    </Box>
  );
};

export default About;

const AboutContent = ({ open, onClose }) => {
  const [loading, setLoading] = useState(false);
  const [logoSrc, setLogoSrc] = useState();
  const { t } = useTranslation();

  useEffect(() => {
    const lang = getStoreLang();
    if (lang == "en" || lang == "es") {
      setLogoSrc(enLogo);
    } else {
      setLogoSrc(zhLogo);
    }
  }, []);

  // 加载图标的样式
  const loadingStyle = {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    height: "calc(400px)",
  };

  return (
    <BootstrapDialog
      fullWidth
      // maxWidth="md"
      // sx={{ minHeight: "800px" }}
      open={open}
      onClose={onClose}
      aria-labelledby="customized-dialog-title">
      <BootstrapDialogTitle onClose={onClose}>
        <Typography variant="h4" component="p">
          {t("system.media_personal_about")}
        </Typography>
      </BootstrapDialogTitle>
      <BootstrapContent dividers={true}>
        <div style={loading ? loadingStyle : null}>
          {loading ? (
            <CircularProgress />
          ) : (
            <Grid container spacing={0} sx={{ minHeight: 350 }}>
              <Grid item xs={6}>
                <Grid container>
                  <Grid item xs={12}>
                    <img src={logoSrc} height={40} alt="" srcset="" />
                  </Grid>
                  <Grid item xs={12}>
                    <Grid container sx={{ width: "100%" }} spacing={[0, 1]}>
                      <Grid item xs={12}>
                        <Stack
                          direction="column"
                          justifyContent="flex-start"
                          alignItems="flex-start"
                          spacing={0}>
                          <Typography
                            variant="body1"
                            display="block"
                            sx={{ fontWeight: "bold" }}
                            component="p">
                            {t("common.common_system_version")}
                          </Typography>
                          <Typography variant="body2" gutterBottom>
                            {/* {info?.name} {info?.version} */}
                            SCREEN DIRECT 3.3.8.SNAPSHOT (Build:20250724)
                          </Typography>
                        </Stack>
                      </Grid>
                      <Grid item xs={12}>
                        <Stack
                          direction="column"
                          justifyContent="flex-start"
                          alignItems="flex-start"
                          spacing={0}>
                          <Typography
                            variant="body1"
                            display="block"
                            sx={{ fontWeight: "bold" }}
                            component="p">
                            {t("system.base_system_browsers")}
                          </Typography>
                          <Typography variant="body2" gutterBottom>
                            Firefox 27+/Chrome 33+/Edge
                          </Typography>
                        </Stack>
                      </Grid>
                      <Grid item xs={12}>
                        <Stack
                          direction="column"
                          justifyContent="flex-start"
                          alignItems="flex-start"
                          spacing={0}>
                          <Typography
                            variant="body1"
                            display="block"
                            sx={{ fontWeight: "bold" }}
                            component="p">
                            {t("system.base_system_resolution")}
                          </Typography>
                          <Typography variant="body2" gutterBottom>
                            {t("common.common_system_resoltion_recommond")}
                          </Typography>
                        </Stack>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          )}
        </div>
      </BootstrapContent>
      <BootstrapActions>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Stack
              direction="row"
              justifyContent="center"
              alignItems="center"
              spacing={2}>
              <img
                src={logoSrc}
                alt=""
                style={{
                  width: "80px",
                  height: "38px",
                }}
                srcset=""
              />
              <Typography variant="button" display="block" gutterBottom>
                Copyright 2023
              </Typography>
            </Stack>
          </Grid>
        </Grid>
      </BootstrapActions>
    </BootstrapDialog>
  );
};
