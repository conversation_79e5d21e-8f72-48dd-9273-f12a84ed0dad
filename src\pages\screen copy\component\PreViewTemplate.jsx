import React from "react";
import PreView from "../../editor/PreView";
function PreViewTemplate(props) {
  const { preViewOpen, setPreViewOpen, setPreViewItem, preViewItem } = props;
  return (
    <React>
      <Dialog
        open={preViewOpen}
        onClose={() => {
          setPreViewItem(null);
          setPreViewOpen(false);
        }}
        maxWidth={"md"}
        style={{ backdropFilter: "blur(5px)" }}>
        {preViewItem?.templateJson && preViewOpen && (
          <PreView layoutJSON={JSON.parse(preViewItem.templateJson)}></PreView>
        )}
      </Dialog>
    </React>
  );
}

export default PreViewTemplate;
