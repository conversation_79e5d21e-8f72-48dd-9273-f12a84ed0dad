
import { getPage } from "@s/PictureService.js";
import CommonUtil from "@u/CommonUtils";
import { toast } from "react-toastify";





export const loadData = (filters, setRecords, setTotalRecords, setLoading) => {
  setLoading(true)
  getPage(filters).then((response) => {
    setRecords(response?.data?.data);
    setTotalRecords(response?.data?.total);
    setLoading(false)
  });
};

export const validateForm = (payload) => {
  if (payload.pictureName.length > 50) {
    setError({
      ...error,
      pictureName: `${t("tips_picture.character_50")}`,
    });
    return;
  }

  if (payload.pictureId.length > 100) {
    setError({
      ...error,
      pictureId: `${t("tips_picture.character_100")}`,
    });
    return;
  }

  if (
    !CommonUtil.isEmpty(payload.pictureName) &&
    toString(payload.firstName).length > 50
  ) {
    setError({
      ...error,
      pictureName: `${t("tips_picture.character_name")}`,
    });
    return;
  }

  if (CommonUtil.isEmptyString(payload.pictureId.trim())) {
    setError({
      ...error,
      pictureId: `${t("tips.required")}`,
    });
    return;
  }
  if (CommonUtil.isEmptyString(payload.pictureProcessing)) {
    setError({
      ...error,
      pictureProcessing: `${t("tips.required")}`,
    });
    return;
  }

  if (CommonUtil.isEmptyString(payload.pictureType)) {
    setError({
      ...error,
      pictureType: `${t("tips.required")}`,
    });
    return;
  }

  return true;
};

export const handleImage = (file, setImage, setBase64, t) => {
  let maxSize = 2 * 1024 * 1024;

  if (
    file.file.type === "image/png" ||
    file.file.type === "image/jpeg" ||
    file.file.type === "image/bmp"
  ) {
    let fileSize = file.file.size;
    let size = parseInt(fileSize);
    if (size <= maxSize) {
      const img = new Image();
      img.onload = () => {
        setBase64(file.base64);
        setImage(file.file);
      };
      img.src = file.base64;
    } else {
      toast.error(t("tips_picture.file_size"));
    }
  } else {
    toast.error(t("tips_product.upload_image_format"));
  }
};


