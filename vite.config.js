import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react";
import qiankun from "vite-plugin-qiankun";
import path from "path";
import tailwindcss from "tailwindcss";
import autoprefixer from "autoprefixer";
import svgr from "vite-plugin-svgr";
import AutoImport from "unplugin-auto-import/vite";
import reactRefresh from "@vitejs/plugin-react-refresh";
import { name } from "./package.json";
import commonjs from 'vite-plugin-commonjs';
import { setupUnPluginIcon } from "./build/icon.js";
import { wrapperEnv } from "./src/util/getEnv";
import cssInjectedByJsPlugin from "vite-plugin-css-injected-by-js";

export default defineConfig((mode) => {
  const env = loadEnv(mode.mode, process.cwd());
  const viteEnv = wrapperEnv(env);
  return {
    base:
      process.env.NODE_ENV === "development" ? "./" : "/e-price-tag-app",
    plugins: [
      // 在开发模式下需要把react()关掉
      // https://github.com/umijs/qiankun/issues/1257
      react({
        fastRefresh: false, // 禁用 react-refresh
      }),
      setupUnPluginIcon(viteEnv),
      reactRefresh(),
      commonjs(),
      qiankun(name, {
        // 微应用名字，与主应用注册的微应用名字保持一致
        useDevMode: process.env.NODE_ENV === "development", // 仅开发模式启用,
      }),
      svgr({
        svgrOptions: {},
      }),

      AutoImport({
        // targets to transform
        include: [
          /\.[tj]sx?$/, // .ts, .tsx, .js, .jsx
          /\.vue$/,
          /\.vue\?vue/, // .vue
          /\.md$/, // .md
        ],

        // global imports to register
        imports: [
          "react",
          "react-router-dom",
          "react-i18next",
          {
            "@mui/material": [
              "Accordion",
              "AccordionActions",
              "AccordionDetails",
              "AccordionSummary",
              "Alert",
              "AlertTitle",
              "AppBar",
              "Autocomplete",
              "Avatar",
              "AvatarGroup",
              "Backdrop",
              "Badge",
              "BottomNavigation",
              "BottomNavigationAction",
              "Box",
              "Breadcrumbs",
              "Button",
              "ButtonBase",
              "ButtonGroup",
              "Card",
              "CardActionArea",
              "CardActions",
              "CardContent",
              "CardHeader",
              "CardMedia",
              "Checkbox",
              "Chip",
              "CircularProgress",
              "ClickAwayListener",
              "Collapse",
              "Container",
              "CssBaseline",
              "darkScrollbar",
              "Dialog",
              "DialogActions",
              "DialogContent",
              "DialogContentText",
              "DialogTitle",
              "Divider",
              "Drawer",
              "Fab",
              "Fade",
              "FilledInput",
              "FormControl",
              "FormControlLabel",
              "FormGroup",
              "FormHelperText",
              "FormLabel",
              "generateUtilityClass",
              "generateUtilityClasses",
              "GlobalStyles",
              "Grid",
              "Grow",
              "Hidden",
              "Icon",
              "IconButton",
              "ImageList",
              "ImageListItem",
              "ImageListItemBar",
              "Input",
              "InputAdornment",
              "InputBase",
              "InputLabel",
              "LinearProgress",
              "List",
              "ListItem",
              "ListItemAvatar",
              "ListItemButton",
              "ListItemIcon",
              "ListItemSecondaryAction",
              "ListItemText",
              "ListSubheader",
              "Menu",
              "MenuItem",
              "MenuList",
              "MobileStepper",
              "Modal",
              "NativeSelect",
              "NoSsr",
              "OutlinedInput",
              "Pagination",
              "PaginationItem",
              "Paper",
              "Popover",
              "Popper",
              "Portal",
              "Radio",
              "RadioGroup",
              "Rating",
              "ScopedCssBaseline",
              "Select",
              "Skeleton",
              "Slide",
              "Slider",
              "Snackbar",
              "SnackbarContent",
              "SpeedDial",
              "SpeedDialAction",
              "SpeedDialIcon",
              "Stack",
              "Step",
              "StepButton",
              "StepConnector",
              "StepContent",
              "StepIcon",
              "StepLabel",
              "Stepper",
              "SvgIcon",
              "SwipeableDrawer",
              "Switch",
              "Tab",
              "Table",
              "TableBody",
              "TableCell",
              "TableContainer",
              "TableFooter",
              "TableHead",
              "TablePagination",
              "TableRow",
              "TableSortLabel",
              "Tabs",
              "TabScrollButton",
              "TextareaAutosize",
              "TextField",
              "ToggleButton",
              "ToggleButtonGroup",
              "Toolbar",
              "Tooltip",
              "Typography",
              "Unstable_Grid2",
              "useAutocomplete",
              "useMediaQuery",
              "useScrollTrigger",
              "Zoom",
            ],

            dayjs: [["default", "dayjs"]],
          },
        ],
        // Array of strings of regexes that contains imports meant to be filtered out.
        ignore: ["useMouse", "useFetch"],

        // Enable auto import by filename for default module exports under directories
        defaultExportByFilename: false,

        // Auto import for module exports under directories
        // by default it only scan one level of modules under the directory
        dirs: [
          // './hooks',
          // './composables' // only root modules
          // './composables/**', // all nested modules
          // ...
        ],

        // Filepath to generate corresponding .d.ts file.
        // Defaults to './auto-imports.d.ts' when `typescript` is installed locally.
        // Set `false` to disable.
        dts: "./auto-imports.d.ts",

        // Array of strings of regexes that contains imports meant to be ignored during
        // the declaration file generation. You may find this useful when you need to provide
        // a custom signature for a function.
        ignoreDts: ["ignoredFunction", /^ignore_/],

        // Auto import inside Vue template
        // see https://github.com/unjs/unimport/pull/15 and https://github.com/unjs/unimport/pull/72
        vueTemplate: false,

        // Auto import directives inside Vue template
        // see https://github.com/unjs/unimport/pull/374
        vueDirectives: undefined,

        // Custom resolvers, compatible with `unplugin-vue-components`
        // see https://github.com/antfu/unplugin-auto-import/pull/23/
        resolvers: [
          /* ... */
        ],

        // Include auto-imported packages in Vite's `optimizeDeps` options
        // Recommend to enable
        viteOptimizeDeps: true,

        // Inject the imports at the end of other imports
        injectAtEnd: true,

        // Generate corresponding .eslintrc-auto-import.json file.
        // eslint globals Docs - https://eslint.org/docs/user-guide/configuring/language-options#specifying-globals
        eslintrc: {
          enabled: false, // Default `false`
          // provide path ending with `.mjs` or `.cjs` to generate the file with the respective format
          filepath: "./.eslintrc-auto-import.json", // Default `./.eslintrc-auto-import.json`
          globalsPropValue: true, // Default `true`, (true | false | 'readonly' | 'readable' | 'writable' | 'writeable')
        },

        // Generate corresponding .biomelintrc-auto-import.json file.
        // biomejs extends Docs - https://biomejs.dev/guides/how-biome-works/#the-extends-option
        biomelintrc: {
          enabled: false, // Default `false`
          filepath: "./.biomelintrc-auto-import.json", // Default `./.biomelintrc-auto-import.json`
        },

        // Save unimport items into a JSON file for other tools to consume
        dumpUnimportItems: "./auto-imports.json", // Default `false`
      }),

      cssInjectedByJsPlugin(),
    ],


    css: {
      postcss: {
        plugins: [tailwindcss, autoprefixer],
      },
    },

    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
        "@c": path.resolve(__dirname, "./src/components"),
        "@s": path.resolve(__dirname, "./src/services"),
        "@a": path.resolve(__dirname, "./src/assets"),
        "@p": path.resolve(__dirname, "./src/pages"),
        "@u": path.resolve(__dirname, "./src/util"),
      },

    },
    server: {
      port: 8083,
      host: "0.0.0.0",
      cors: true,
      headers: {
        "Access-Control-Allow-Origin": "*",
      },
      origin: "http://localhost:8080",
      proxy: {
        "/dev": {
          // 推荐不要直接修改下面的地址，查看同级目录下的local-env.js.sample文件介绍
          // target: "http://**********:9090",
          // target: "http://**********:9090",
          // target: "http://***********:9090",
          target: "http://**********:9090",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/dev/, ""),
        },
      },
    },

    // 修改 build 配置
    // build: process.env.QIANKUN
    //   ? {
    //     target: "esnext",
    //     cssCodeSplit: false,
    //     outDir: 'dist',
    //     sourcemap: true,
    //     rollupOptions: {
    //       output: {
    //         format: "umd",
    //         name: name,
    //         entryFileNames: `${name}-[name].js`,
    //         chunkFileNames: `${name}-[name].js`,
    //         assetFileNames: `${name}-[name].[ext]`,
    //         globals: {
    //           react: "React",
    //           "react-dom": "ReactDOM",
    //         },
    //       },
    //     },
    //   }
    //   : undefined,

    build: {
      target: "esnext",
      cssCodeSplit: false,
      outDir: 'dist',
      sourcemap: true,
      rollupOptions: {
        output: {
          format: "umd",
          name: name,
          entryFileNames: `${name}-[name].js`,
          chunkFileNames: `${name}-[name].js`,
          assetFileNames: `${name}-[name].[ext]`,
          globals: {
            react: "React",
            "react-dom": "ReactDOM",
          },
        },
      },
    },
  }
});
