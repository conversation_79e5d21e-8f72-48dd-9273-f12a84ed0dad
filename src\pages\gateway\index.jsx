import React from "react";
import ListLayout from "@c/ListLayout";
import { getGateWay } from "@s/common.js";
import { debounce } from "lodash-es";
import TableList from "./TableList.jsx";
function index() {
  const { t } = useTranslation();

  const [data, setData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isRefetching, setIsRefetching] = useState(false);
  const [isError, setIsError] = useState(false);
  const [rowCount, setRowCount] = useState(0);

  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 5,
  });

  const buildParams = () => {
    const params = {
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
      category: 1,
    };

    return params;
  };

  const getTableData = useCallback(
    debounce(() => {
      if (!data.length) {
        setIsLoading(true);
      } else {
        setIsRefetching(true);
      }

      getGateWay(buildParams())
        .then((res) => {
          // 设置数据
          setData(res.data.data);
          // 设置总记录数
          setRowCount(res.data.total);
          setIsLoading(false);
          setIsRefetching(false);
        })
        .catch((err) => {
          setIsError(true);
          setIsLoading(false);
          setIsRefetching(false);
        });
    }, 50),
    [pagination.pageIndex, pagination.pageSize]
  );

  useEffect(() => {
    getTableData();
  }, [pagination]);

  const rederTable = () => {
    return (
      <TableList
        data={data}
        isLoading={isLoading}
        isRefetching={isRefetching}
        isError={isError}
        rowCount={rowCount}
        pagination={pagination}
        setPagination={setPagination}
        getTableData={getTableData}></TableList>
    );
  };

  return (
    <React.Fragment>
      <ListLayout title={t("menu.gateway")} content={rederTable()}></ListLayout>
    </React.Fragment>
  );
}

export default index;
