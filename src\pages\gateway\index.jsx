import React from "react";
import ListLayout from "@c/ListLayout";
import DataTable from "@c/DataTable";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";

import { getColums } from "./Colums.jsx";
import { getGateWay } from "@s/common.js";
import { useStatePermission } from "@/hooks/user";
function index() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const permission = JSON.parse(
    sessionStorage.getItem("USER_INFO")
  )?.permissions;
  const [loading, setLoading] = useState(false);
  const [records, setRecords] = useState([]);
  const [totalRecords, setTotalRecords] = React.useState(1);
  const [filters, setFilters] = useState({
    page: 1,
    pageSize: 5,
    category: 1,
  });

  const defaultFilters = {
    page: 1,
    pageSize: 5,
    category: 1,
  };

  useEffect(() => {
    loadData();
  }, [filters]);

  const toolbarProps = {
    // add:
    //   permission &&
    //   (permission.includes("nt:nutag:device:save") ||
    //     permission.includes("*:*:*")),

    add: false,
    filter: false,
    refresh: true,
    onAdd: (data) => {
      console.log("onAdd");
    },
    onRefresh: (data) => {
      loadData();
    },
    onFilter: (data) => {
      console.log("onFilter");
    },
  };

  const loadData = () => {
    setLoading(true);
    getGateWay(filters).then((res) => {
      if (res.code == "00000000") {
        setRecords(res?.data?.data);
        setTotalRecords(res?.data?.total);
      }
    });

    setLoading(false);
  };

  const handlePageChange = (e) => {
    setFilters({ ...filters, page: e + 1 });
  };

  const handlePageSize = (e) => {
    setFilters({
      ...defaultFilters,
      page: defaultFilters.page,
      pageSize: e,
    });
  };

  return (
    <React.Fragment>
      <ListLayout
        navigateBack={false}
        title={t("menu.gateway")}
        globalFilterProps={false}
        toolbarProps={toolbarProps}>
        <DataTable
          columns={getColums(t, loadData)}
          rows={records}
          onSelection={() => console.log()}
          getRowId={(device) => device.id}
          page={filters.page - 1}
          totalRecords={totalRecords}
          rowsPerPage={filters.pageSize}
          onPageChange={(pn) => handlePageChange(pn)}
          onPageSizeChange={(ps) => handlePageSize(ps)}
        />
      </ListLayout>
    </React.Fragment>
  );
}

export default index;
