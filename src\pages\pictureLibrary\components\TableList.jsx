import React, { useMemo } from "react";
import { useNavigate } from "react-router-dom";
import ZktecoTable from "@c/ZktecoTable";
import { useTranslation } from "react-i18next";
import { useConfirm } from "@/components/zkconfirm";
import { toast } from "react-toastify";
import { deletes } from "@s/PictureService.js";

function TableList({
  data,
  isLoading,
  isRefetching,
  isError,
  rowCount,
  pagination,
  setPagination,
  getTableData,
}) {
  const { t } = useTranslation();

  const navigate = useNavigate();
  const confirmFn = useConfirm();

  const permission = JSON.parse(
    sessionStorage.getItem("USER_INFO")
  )?.permissions;

  const columns = useMemo(
    () => [
      {
        accessorKey: "pictureName",
        header: t("picture_library.name"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },
      {
        accessorKey: "pictureId",
        header: t("picture_library.id"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },
      {
        accessorKey: "url",
        header: t("picture_library.preview"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
        Cell: ({ row }) => {
          return (
            <>
              <Avatar
                sx={{
                  width: "121px",
                  height: "50px",
                  borderRadius: "8px",
                  border: "1px solid #E3E3E3",
                }}
                size="medium"
                alt={row.original.pictureName}
                src={row.original.url}
                variant="circular"></Avatar>
            </>
          );
        },
      },
      {
        accessorKey: "pictureType",
        header: t("picture_library.type"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
        Cell: ({ row }) => {
          return (
            <>
              <Tooltip>
                <span>
                  {row.original.pictureType == "0"
                    ? t("picture_library.general_user")
                    : t("picture_library.company_logo")}
                </span>
              </Tooltip>
            </>
          );
        },
      },

      {
        accessorKey: "pictureProcessing",
        header: t("picture_library.progressing"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
        Cell: ({ row }) => {
          return (
            <>
              <Tooltip>
                <span>
                  {row.original.pictureProcessing == "0"
                    ? t("picture_library.original_image")
                    : t("picture_library.dithering_image")}
                </span>
              </Tooltip>
            </>
          );
        },
      },
    ],
    []
  );

  // 删除
  const handlerDelete = async (data) => {
    confirmFn({
      title: t("tips.selected_delete_record"),
      confirmationText: t("common.delete"),
      cancellationText: t("common.cancel"),
      description: t("common.common_confirm_delete"),
    })
      .then(() => {
        let ids = [];
        ids.push(data?.id);
        deletes({
          ids: ids,
        }).then((res) => {
          toast.success(res.message);
          getTableData();
        });
      })
      .catch(() => {
        // 用户取消删除操作，不执行任何操作
      });
  };

  const isShowAction = {
    isShowEditor: "nt:nutag:picture_library:update",
    isShowDetele: "nt:nutag:picture_library:delete",
  };

  const handlePageChange = useCallback(
    (pageIndex) => {
      setPagination((prev) => ({ ...prev, pageIndex }));
    },
    [setPagination]
  );

  const handlePageSizeChange = useCallback(
    (pageSize) => {
      setPagination({ pageIndex: 0, pageSize });
    },
    [setPagination]
  );

  const actionHandlers = useMemo(
    () => ({
      handlerView: (data) =>
        navigate("/view/picture-library", {
          state: { id: data?.id, type: "view" },
        }),
      handlerEditor: (data) =>
        navigate("/add/picture-library", {
          state: { id: data?.id, type: "editor" },
        }),

      Detele: (data) => {
        handlerDelete(data);
      },
    }),
    []
  );

  return (
    <ZktecoTable
      columns={columns}
      data={data}
      rowCount={rowCount}
      isLoading={isLoading}
      isRefetching={isRefetching}
      isError={isError}
      pathRoute="/add/picture-library"
      loadDada={getTableData}
      paginationProps={{
        currentPage: pagination.pageIndex,
        rowsPerPage: pagination.pageSize,
        onPageChange: handlePageChange,
        onPageSizeChange: handlePageSizeChange,
      }}
      topActions={{
        showAdd:
          permission &&
          (permission.includes("nt:nutag:picture_library:save") ||
            permission.includes("*:*:*")),
      }}
      actionHandlers={actionHandlers}
      isShowAction={isShowAction}
    />
  );
}

export default TableList;
