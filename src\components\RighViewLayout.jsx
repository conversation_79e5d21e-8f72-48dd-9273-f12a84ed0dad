import { Grid } from "@mui/material";
import React, { useContext, useEffect, useState } from "react";
import TitleBar from "./TitleBar";

import AppContext from "@/context/AppContext";

export default function RightViewLayout(props) {
  const [clients, setClients] = useState([]);
  const [client, setClient] = useState(
    JSON.parse(localStorage.getItem("selectedClient"))
  );
  const { setSelectedClient } = useContext(AppContext);

  useEffect(() => {
    if (client && JSON.stringify(client) !== "{}") {
      setSelectedClient(client);
      localStorage.setItem("selectedClient", JSON.stringify(client));
      localStorage.setItem("selectedClientId", client?.id);
    } else {
      localStorage.setItem("selectedClientId", "-1");
      localStorage.setItem("selectedClient", JSON.stringify({}));
    }
  }, [client]);

  useEffect(() => {
    if (clients.length > 0 && localStorage.getItem("selectedClient")) {
      setClient(JSON.parse(localStorage.getItem("selectedClient")));
    }
  }, [clients]);

  return (
    <Grid container>
      {!props.navigateBack && props.title == null ? null : (
        <Grid
          xs={12}
          item
          sx={{ display: "flex", justifyContent: "space-between" }}>
          <Grid item xs={2}>
            <TitleBar
              navigateBack={props.navigateBack}
              title={props.title ? props.title : ""}
              actions={props.actions}
            />
          </Grid>

          {props.isShowSearch && (
            <Grid item m={3}>
              {props.searchProps}
            </Grid>
          )}
        </Grid>
      )}

      <Grid
        item
        xs={12}
        sx={{
          padding: "10px",
        }}>
        {props.children}
      </Grid>
    </Grid>
  );
}
