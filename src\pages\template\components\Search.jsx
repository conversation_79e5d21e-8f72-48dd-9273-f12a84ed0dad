import React from "react";
import SearchIcon from "@mui/icons-material/Search";
import { useTranslation } from "react-i18next";
import { list } from "@s/resolution.js";
function Search(props) {
  const {
    setFilters,
    resolution = null,
    setResolution,
    templateValue = null,
    setTemplateValue,
  } = props;
  const { t } = useTranslation();

  let templateTypeOptions = [
    { id: "0", value: t("dictionary.generaluse") },
    { id: "1", value: t("dictionary.discount") },
    { id: "2", value: t("dictionary.byunit") },
    { id: "3", value: t("dictionary.by_value") },
    { id: "4", value: t("dictionary.promotion") },
  ];

  const [resolutionOptions, setResolutionOptions] = useState([]);

  const handleResolutionChange = () => {
    setFilters({
      resolution: resolution,
      promotionType: templateValue?.id,
      operator: "AND",
    });
  };

  const handleReset = () => {
    setResolution(null); // 改为 null 而不是空字符串
    setTemplateValue(null); // 改为 null 而不是空字符串
    setFilters({
      page: 1,
      pageSize: 5,
      name: "",
      promotionType: "",
      resolution: "",
    });
  };

  useEffect(() => {
    list().then((res) => {
      setResolutionOptions(res?.data?.valueList || []);
    });
  }, []);

  return (
    <React.Fragment>
      {/* <Typography variant="title" sx={{ fontSize: "20px" }}>
        {t("menu.template_list")}
      </Typography> */}
      <Grid
        container
        xs={12}
        spacing={4}
        style={{
          height: "100px",
          background: "#FFFFFF",
          borderRadius: "8px",
          marginTop: "12px",
          marginLeft: "4px",
        }}>
        <Grid item xs={2} mt={-2.5}>
          <Typography
            sx={{
              fontSize: "14px",
              color: "#474B4F",
              opacity: "0.8",
              marginBottom: "8px",
            }}>
            {t("template.resolution")}
          </Typography>
          <Autocomplete
            noOptionsText={t("tips.no_options")}
            options={resolutionOptions || []}
            value={resolution}
            onChange={(e, v) => {
              setResolution(v);
            }}
            getOptionLabel={(option) => (option ? option : "")}
            renderInput={(params) => (
              <TextField
                {...params}
                placeholder={t("Please select screen resolution")}
                size="small"
              />
            )}
          />
        </Grid>
        <Grid item xs={2} mt={-2.5}>
          <Typography
            sx={{
              fontSize: "14px",
              color: "#474B4F",
              opacity: "0.8",
              marginBottom: "8px",
            }}>
            {t("template.type")}
          </Typography>
          <Autocomplete
            noOptionsText={t("tips.no_options")}
            options={templateTypeOptions || []}
            value={templateValue}
            onChange={(e, v) => {
              setTemplateValue(v);
            }}
            isOptionEqualToValue={(option, value) =>
              (!option && !value) ||
              (option && value && option.value === value.value)
            }
            getOptionLabel={(option) => (option ? option.value : "")}
            renderInput={(params) => (
              <TextField
                {...params}
                placeholder={t("Please select template type")}
                size="small"
              />
            )}
          />
        </Grid>

        <Grid item mt={1} ml={4}>
          <Button
            id="index1"
            variant="outlined"
            size="medium"
            className="text-transform-none"
            onClick={handleResolutionChange}
            style={{
              background: "linear-gradient(45deg, #1487CA, #78BC27)",
              marginRight: "10px",
              color: "#FFFF",
            }}>
            {t("common.find")}
          </Button>
          <Button
            id="index2"
            className="text-transform-none"
            variant="outlined"
            size="medium"
            style={{ marginRight: "10px" }}
            onClick={handleReset}>
            {t("common.reset")}
          </Button>
        </Grid>
      </Grid>
    </React.Fragment>
  );
}

export default Search;
