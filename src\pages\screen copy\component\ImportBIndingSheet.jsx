import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Bootstra<PERSON>Content,
  BootstrapD<PERSON>ogTitle,
  BootstrapDialog,
} from "@/components/dialog/index.js";
import { Typo<PERSON>, Button, Box, Grid } from "@mui/material";
import { useTranslation } from "react-i18next";
import GroupIcon from "@/assets/Icons/Group 25808.svg?react";
import ArrowCircleDownIcon from "@mui/icons-material/ArrowCircleDown"; // 添加这个导入
import {
  getDownloadExcel,
  importScreen,
  importProgress,
} from "@/services/screens";
import { toast } from "react-toastify";
import { LinearProgress } from "@mui/material"; // {
function ImportBIndingSheet(props) {
  const { open, onClose } = props;
  const { t } = useTranslation();
  const [selectedFile, setSelectedFile] = useState(null);
  const [progress, setProgress] = useState(0);
  const [isImporting, setIsImporting] = useState(false);
  const [taskId, setTaskId] = useState(null);
  const initProgressDetails = {
    total: 0,
    processed: 0,
    success: 0,
    failed: 0,
    percent: 0,
    status: "PENDING", // PENDING, RUNNING, SUCCESS, FAILED
    message: "",
    errors: [],
    successRate: 0,
    failureRate: 0,
  };
  const statusMap = {
    PENDING: t("screen.status_pending"),
    RUNNING: t("screen.status_running"),
    SUCCESS: t("screen.status_success"),
    FAILED: t("screen.status_failed"),
  };
  // 新增详细进度状态
  const [progressDetails, setProgressDetails] = useState(initProgressDetails);
  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    setSelectedFile(file);
  };

  // 重置所有状态
  const resetProgress = () => {
    setProgress(0);
    setIsImporting(false);
    setTaskId(null);
    setSelectedFile(null);
    setProgressDetails(initProgressDetails);
  };

  // 处理对话框关闭
  const handleClose = () => {
    if (!isImporting) {
      resetProgress();
      onClose();
    }
  };

  // 格式化进度数据
  const formatProgressData = (data) => {
    if (!data) return initProgressDetails;

    let percent =
      data.total > 0 ? ((data.processed || 0) / data.total) * 100 : 0;
    if (
      data.total > 0 &&
      data.processed === data.total &&
      data.success + data.failed < data.total
    ) {
      percent = 99;
    }
    return {
      total: data.total || 0,
      processed: data.processed || 0,
      success: data.success || 0,
      failed: data.failed || 0,
      percent,
      status: data.status || "RUNNING",
      message: data.message || "",
      errors: data.errors || [],
      // 计算额外字段
      remaining: (data.total || 0) - (data.processed || 0),
      successRate:
        data.total > 0
          ? (((data.success || 0) / data.total) * 100).toFixed(2)
          : 0,
      failureRate:
        data.total > 0
          ? (((data.failed || 0) / data.total) * 100).toFixed(2)
          : 0,
    };
  };

  const handleImportAndRefresh = async () => {
    try {
      setIsImporting(true);
      setProgress(0);
      const response = await importScreen({ file: selectedFile });
      setTaskId(response.data);
      // 导入开始后，进度更新将由 useEffect 处理
    } catch (error) {
      setIsImporting(false);
      toast.error("Import failed");
    }
  };

  const handleDownloadTemplate = async () => {
    try {
      const response = await getDownloadExcel();

      // 创建一个Blob对象
      const blob = new Blob([response], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });

      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", "binding_template.xlsx");

      // 触发下载
      document.body.appendChild(link);
      link.click();

      // 清理
      link.parentNode.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Download failed:", error);
      toast.error("Failed to download template");
    }
  };

  useEffect(() => {
    let intervalId;
    if (isImporting && taskId) {
      intervalId = setInterval(async () => {
        try {
          const progressData = await importProgress(taskId);
          const data = progressData?.data;

          if (data) {
            // 使用格式化函数处理进度数据
            const newProgressDetails = formatProgressData(data);

            setProgressDetails(newProgressDetails);
            setProgress(newProgressDetails.percent);

            // 检查导入状态
            if (
              newProgressDetails.status === "SUCCESS" ||
              newProgressDetails.success + newProgressDetails.failed >=
                newProgressDetails.total
            ) {
              setIsImporting(false);
              clearInterval(intervalId);

              // 根据成功失败情况显示不同消息
              if (newProgressDetails.failed > 0) {
                toast.warning(
                  t("screen.import_completed_with_success_and_failures", {
                    success: newProgressDetails.success,
                    failed: newProgressDetails.failed,
                  })
                );
              } else {
                toast.success(
                  t("screen.import_completed_successfully", {
                    success: newProgressDetails.success,
                  })
                );
              }

              // 如果有错误，可以在控制台显示详细信息
              if (newProgressDetails.errors.length > 0) {
                console.warn("Import errors:", newProgressDetails.errors);
              }
            } else if (newProgressDetails.status === "FAILED") {
              setIsImporting(false);
              clearInterval(intervalId);
              // toast.error(`Import failed: ${newProgressDetails.message}`);
              toast.error(t("screen.import_failed"));
            }
          }
        } catch (error) {
          console.error("Failed to fetch progress:", error);
          setIsImporting(false);
          clearInterval(intervalId);
          toast.error(t("screen.failed_fetch_import_progress"));
        }
      }, 2000); // 每2秒更新一次进度
    }
    return () => clearInterval(intervalId);
  }, [isImporting, taskId]);

  return (
    <React.Fragment>
      <BootstrapDialog
        fullWidth
        maxWidth="sm"
        open={open}
        onClose={handleClose}
        aria-labelledby="customized-dialog-title"
      >
        <BootstrapDialogTitle>
          <Typography
            style={{
              font: `normal normal normal 16px/18px Roboto`,
              color: `#000`,
            }}
          >
            {t("screen.import_binding_sheet")}
          </Typography>
        </BootstrapDialogTitle>

        <BootstrapContent>
          <Grid
            sx={{
              mb: 3,
              height: "90px",
              background: `#f3f9fc 0% 0% no-repeat padding-box`,
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              pl: 2,
              pr: 2,
            }}
          >
            <Typography
              sx={{
                font: `normal normal normal 16px/20px Roboto`,
                color: "#474B4F",
                opacity: 0.8,
              }}
            >
              {t("screen.click_download_sample_file")}
            </Typography>
            <Button
              variant="outlined"
              sx={{
                color: "#1976d2",
                border: `1px solid #1487CA`,
                width: "212px",
                height: "50px",
              }}
              startIcon={<ArrowCircleDownIcon />} // 添加圆形向下箭头图标
              onClick={handleDownloadTemplate}
            >
              {t("screen.download_template")}
            </Button>
          </Grid>

          <Box
            sx={{
              border: "1px solid #e0e0e0",
              borderRadius: "8px",
              p: 4,
              textAlign: "center",
              backgroundColor: "#fafafa",
              minHeight: "200px",
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              position: "relative",
            }}
          >
            <input
              type="file"
              accept=".xlsx,.xls,.csv"
              onChange={handleFileSelect}
              style={{
                position: "absolute",
                top: 0,
                left: 0,
                width: "100%",
                height: "100%",
                opacity: 0,
                cursor: "pointer",
              }}
            />

            <GroupIcon
              style={{ width: "80px", height: "80px", marginBottom: "16px" }}
            />

            {(progress > 0 || isImporting) && (
              <Box sx={{ width: "100%", mt: 2 }}>
                <LinearProgress variant="determinate" value={progress} />
                <Typography
                  variant="body2"
                  color="text.secondary"
                  align="center"
                  sx={{ mt: 1 }}
                >
                  {`${Math.round(progress)}%`}
                </Typography>

                {/* 详细进度信息 */}
                {progressDetails.total > 0 && (
                  <Box sx={{ mt: 2, textAlign: "left" }}>
                    <Typography
                      variant="body2"
                      sx={{ fontSize: "12px", color: "#666" }}
                    >
                      <strong>{t("screen.total_colon")}</strong>{" "}
                      {progressDetails.total} |
                      <strong> {t("screen.processed_colon")}</strong>{" "}
                      {progressDetails.processed} |
                      <strong> {t("screen.remaining_colon")}</strong>{" "}
                      {progressDetails.remaining}
                    </Typography>

                    {progressDetails.processed > 0 && (
                      <Typography
                        variant="body2"
                        sx={{ fontSize: "12px", color: "#666", mt: 0.5 }}
                      >
                        <span style={{ color: "#4caf50" }}>
                          <strong>{t("screen.success_colon")}</strong>{" "}
                          {progressDetails.success} (
                          {progressDetails.successRate}%)
                        </span>
                        {progressDetails.failed > 0 && (
                          <span
                            style={{ color: "#f44336", marginLeft: "10px" }}
                          >
                            <strong>{t("screen.failed_colon")}</strong>{" "}
                            {progressDetails.failed} (
                            {progressDetails.failureRate}%)
                          </span>
                        )}
                      </Typography>
                    )}

                    {progressDetails.status && (
                      <Typography
                        variant="body2"
                        sx={{ fontSize: "12px", color: "#666", mt: 0.5 }}
                      >
                        <strong>{t("screen.status_colon")}</strong>{" "}
                        {statusMap[progressDetails.status] ||
                          t("screen.status_pending")}
                      </Typography>
                    )}

                    {progressDetails.message && (
                      <Typography
                        variant="body2"
                        sx={{ fontSize: "12px", color: "#1976d2", mt: 0.5 }}
                      >
                        {progressDetails.message}
                      </Typography>
                    )}
                  </Box>
                )}
              </Box>
            )}

            {progress > 0 ? null : (
              <Box>
                <Typography
                  sx={{
                    color: "#1976d2",
                    fontSize: "16px",
                    fontWeight: 500,
                    mb: 2,
                  }}
                >
                  {t("screen.choose_file_import")}
                </Typography>

                <Typography
                  variant="body2"
                  sx={{ color: "#666", fontSize: "12px", mb: 1 }}
                >
                  <strong>{t("screen.import_colon")}</strong>{" "}
                  {t("screen.import_desc")}
                </Typography>
                <Typography
                  variant="body2"
                  sx={{ color: "#666", fontSize: "12px" }}
                >
                  <strong>{t("screen.refresh_colon")}</strong>{" "}
                  {t("screen.refresh_desc")}
                </Typography>
              </Box>
            )}

            {selectedFile && (
              <Typography
                sx={{
                  mt: 2,
                  color: "#4caf50",
                  fontSize: "14px",
                }}
              >
                {selectedFile.name}
              </Typography>
            )}
          </Box>
        </BootstrapContent>

        <BootstrapActions>
          <Box sx={{ display: "flex", gap: 2 }}>
            {isImporting && (
              <Button
                variant="outlined"
                onClick={() => {
                  setIsImporting(false);
                  setTaskId(null);
                  toast.info(t("screen.import_cancelled_by_user"));
                }}
                sx={{
                  width: "120px",
                  height: "64px",
                  color: "#666",
                  borderColor: "#666",
                }}
              >
                {t("common.cancel")}
              </Button>
            )}

            <Button
              variant="contained"
              onClick={handleImportAndRefresh}
              disabled={!selectedFile || isImporting}
              sx={{
                width: "180px",
                height: "64px",
                background: `transparent linear-gradient(270deg, #1487CA 0%, #78BC27 100%) 0% 0% no-repeat padding-box`,
                font: `normal normal normal 16px/18px Roboto`,
                color: "#FFFFFF",
              }}
            >
              {isImporting
                ? t("screen.importing")
                : t("screen.import_and_refresh")}
            </Button>
          </Box>
        </BootstrapActions>
      </BootstrapDialog>
    </React.Fragment>
  );
}

export default ImportBIndingSheet;
