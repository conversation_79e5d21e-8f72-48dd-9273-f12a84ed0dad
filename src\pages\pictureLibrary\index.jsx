import React, { useState } from "react";
import { loadData } from "./components/utils";
import { getColumns } from "./components/Columns.jsx";
import ListLayout from "@c/ListLayout";
import DataTable from "@c/DataTable";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { useStatePermission } from "@/hooks/user";
import DeletePicture from "./components/DeletePicture.jsx";
function index() {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const permission = JSON.parse(
    sessionStorage.getItem("USER_INFO")
  )?.permissions;

  const [open, setOpen] = React.useState(false);
  const [deleteId, setDeleteId] = useState("");
  const columns = getColumns(t, navigate, setOpen, setDeleteId);
  const [records, setRecords] = useState([]);
  const [loading, setLoading] = useState(false);
  const [totalRecords, setTotalRecords] = React.useState(0);
  const [rowsPerPage, setRowsPerPage] = React.useState(5);
  const [filters, setFilters] = useState({
    page: 1,
    pageSize: 5,
    name: "",
  });

  const defaultFilters = {
    page: 1,
    pageSize: 5,
    name: "",
  };

  useEffect(() => {
    loadData(filters, setRecords, setTotalRecords, setLoading);
  }, [filters]);

  const toolbarProps = {
    add:
      permission &&
      (permission.includes("nt:nutag:picture_library:save") ||
        permission.includes("*:*:*")),
    filter: false,
    refresh: true,
    onAdd: (data) => {
      navigate("/add/picture-library");
    },
    onRefresh: (data) => {
      setFilters({ ...defaultFilters });
    },
  };

  const handleChangePage = (newPage) => {
    setFilters({
      ...defaultFilters,
      page: newPage + 1,
      pageSize: rowsPerPage,
    });
  };

  const handleChangePageSize = (pageSize) => {
    setFilters({ ...defaultFilters, page: 1, pageSize: pageSize });
    setRowsPerPage(pageSize);
  };

  return (
    <React.Fragment>
      {/* <Grid container>
        <Typography
          style={{
            fontSize: "20px",
            textAlign: "center",
            opacity: "0.8",
            marginTop: "10px",
            marginLeft: "10px",
          }}>
          {t("picture_library.picture_library")}
        </Typography>
      </Grid> */}
      <ListLayout navigateBack={false} toolbarProps={toolbarProps}>
        <DataTable
          columns={columns}
          rows={records}
          page={filters.page - 1}
          onSelection={() => console.log()}
          loading={loading}
          rowsPerPage={filters.pageSize}
          totalRecords={totalRecords}
          onPageChange={(newPage) => handleChangePage(newPage)}
          onPageSizeChange={(pageSize) => handleChangePageSize(pageSize)}
        />

        <DeletePicture
          open={open}
          setOpen={setOpen}
          deleteId={deleteId}
          filters={filters}
          setRecords={setRecords}
          setLoading={setLoading}
          setTotalRecords={setTotalRecords}></DeletePicture>
      </ListLayout>
    </React.Fragment>
  );
}

export default index;
