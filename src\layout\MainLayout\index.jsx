import React from "react";
import { useEffect, useState } from "react";
import { Outlet } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";

import { useTheme } from "@mui/material/styles";
import { Box, Toolbar, useMediaQuery } from "@mui/material";

// project import
import Drawer from "./Drawer";
import Header from "./Header";

// types
import { openDrawer } from "@/store/reducers/menu";

// ==============================|| MAIN LAYOUT ||============================== //
import { useDispatchMenu } from "@/hooks/menu";
import { useStateUserInfo, useDispatchUser } from "@/hooks/user";
import bgUrl from "@/assets/images/bg/GlobalBg.png?react";
const MainLayout = () => {
  const theme = useTheme();
  const matchDownLG = useMediaQuery(theme.breakpoints.down("xl"));
  const dispatch = useDispatch();
  const { stateSetMenuList } = useDispatchMenu();
  const { drawerOpen, backdropOpen } = useSelector((state) => state.menu);
  const { stateSetUser, stateSetPermission } = useDispatchUser();
  const [loading, setLoading] = useState(false);
  // drawer toggler
  const [open, setOpen] = useState(drawerOpen);
  const handleDrawerToggle = () => {
    setOpen(!open);
    dispatch(openDrawer({ drawerOpen: !open }));
  };

  // set media wise responsive drawer
  useEffect(() => {
    setOpen(!matchDownLG);
    dispatch(openDrawer({ drawerOpen: !matchDownLG }));

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [matchDownLG]);

  useEffect(() => {
    if (open !== drawerOpen) setOpen(drawerOpen);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [drawerOpen]);

  return (
    <>
      <div
        className="relative flex flex-row"
        style={{
          height: "100vh",
          width: "100vw",
          backgroundSize: "cover",
          backgroundRepeat: "no-repeat",
          overflow: "hidden",
          borderSizing: "border-box",
          backgroundAttachment: "fixed",
          backgroundImage: `url(${bgUrl})`,
        }}>
        <Header open={open} handleDrawerToggle={handleDrawerToggle} />
        <div className="h-full pl-2 pt-2 pb-2">
          <Drawer
            className={`bg-white h-full border-solid border-zinc-200 border rounded-lg py-2  transition-all duration-300 ease-in-out ${
              open ? "w-[250px]" : "w-0"
            }`}
            open={open}
            handleDrawerToggle={handleDrawerToggle}
          />
        </div>
        <div
          className="h-full flex-1 overflow-y-auto  overflow-x-hidden"
          style={{
            padding: "20px",
            marginTop: "70px",
          }}>
          <Outlet />
        </div>
      </div>
    </>
  );
};

export default MainLayout;
