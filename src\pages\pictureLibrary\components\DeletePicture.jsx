import React from "react";
import { toast } from "react-toastify";
import { deletes } from "@s/PictureService.js";
function DeletePicture(props) {
  const { t } = useTranslation();

  const {
    open,
    setOpen,
    deleteId,
    loadData,
    filters,
    setRecords,
    setTotalRecords,
  } = props;

  return (
    <React.Fragment>
      <Dialog open={open} onClose={() => setOpen(false)} maxWidth="md">
        <DialogTitle>
          <Typography variant="subtitle2">
            {t("tips.selected_delete_record")}
          </Typography>
        </DialogTitle>
        <Grid
          item
          xs={12}
          sm={12}
          md={12}
          lg={12}
          xl={12}
          padding={"10px"}
          justifyContent={"center"}>
          <Box display="flex" justifyContent="center">
            <Button
              variant="outlined"
              color="primary"
              style={{ fontSize: "normal normal normal 14px / 22px Roboto" }}
              onClick={() => setOpen(false)}
              sx={{ marginRight: 2 }}>
              {t("common.cancel")}
            </Button>
            <Button
              variant="contained"
              color="primary"
              style={{
                background:
                  "transparent linear-gradient(270deg, #1487CA 0%, #78BC27 100%)",
                color: "#FFFFFF",
                fontSize: "normal normal normal 14px / 22px Roboto",
                width: "80px",
              }}
              onClick={() => {
                let data = [];
                data.push(deleteId);
                deletes({
                  ids: data,
                }).then((response) => {
                  setOpen(false);
                  toast.success(response.message);
                  loadData(filters, setRecords, setTotalRecords);
                });
              }}>
              {t("common.delete")}
            </Button>
          </Box>
        </Grid>
      </Dialog>
    </React.Fragment>
  );
}

export default DeletePicture;
