import React, { useState, useMemo } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Checkbox,
  IconButton,
  Typography,
  Box,
  Chip,
  Avatar,
  Tooltip,
  TablePagination,
  CircularProgress,
  useTheme,
  TableSortLabel,
  Skeleton,
  Fade,
  Zoom,
} from "@mui/material";
import {
  MoreVert as MoreVertIcon,
  GetApp as DownloadIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  FilterList as FilterIcon,
  Search as SearchIcon,
} from "@mui/icons-material";
import { useTranslation } from "react-i18next";
import { NoResultFound } from "./NoResultFound";

const EnhancedDataTable = ({
  columns = [],
  rows = [],
  loading = false,
  checkboxSelection = false,
  onRowClick,
  onSelectionChange,
  selectedRows = [],
  pagination = true,
  rowsPerPageOptions = [5, 10, 25, 50],
  totalRecords = 0,
  page = 0,
  rowsPerPage = 10,
  onPageChange,
  onRowsPerPageChange,
  actions = [],
  dense = false,
  stickyHeader = false,
  maxHeight = "70vh",
  emptyMessage,
  sortable = false,
  onSort,
  orderBy = "",
  order = "asc",
  showSkeleton = true,
  skeletonRows = 5,
  animateRows = true,
  ...props
}) => {
  const theme = useTheme();
  const { t } = useTranslation();
  const [hoveredRow, setHoveredRow] = useState(null);

  // 处理排序
  const handleSort = (property) => {
    if (!sortable || !onSort) return;

    const isAsc = orderBy === property && order === "asc";
    const newOrder = isAsc ? "desc" : "asc";
    onSort(property, newOrder);
  };

  // 渲染骨架屏
  const renderSkeleton = () => {
    return Array.from({ length: skeletonRows }).map((_, index) => (
      <TableRow key={`skeleton-${index}`}>
        {checkboxSelection && (
          <TableCell padding="checkbox">
            <Skeleton variant="rectangular" width={20} height={20} />
          </TableCell>
        )}
        {columns.map((column) => (
          <TableCell key={column.field}>
            <Skeleton
              variant="text"
              width={`${Math.random() * 40 + 60}%`}
              height={20}
            />
          </TableCell>
        ))}
      </TableRow>
    ));
  };

  // 处理全选
  const handleSelectAllClick = (event) => {
    if (event.target.checked) {
      const newSelected = rows.map((row) => row.id);
      onSelectionChange && onSelectionChange(newSelected);
    } else {
      onSelectionChange && onSelectionChange([]);
    }
  };

  // 处理单行选择
  const handleRowSelect = (event, id) => {
    event.stopPropagation();
    const selectedIndex = selectedRows.indexOf(id);
    let newSelected = [];

    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selectedRows, id);
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selectedRows.slice(1));
    } else if (selectedIndex === selectedRows.length - 1) {
      newSelected = newSelected.concat(selectedRows.slice(0, -1));
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(
        selectedRows.slice(0, selectedIndex),
        selectedRows.slice(selectedIndex + 1)
      );
    }

    onSelectionChange && onSelectionChange(newSelected);
  };

  // 渲染单元格内容
  const renderCellContent = (column, row) => {
    const value = row[column.field];

    if (column.renderCell) {
      return column.renderCell({ row, value });
    }

    // 根据列类型渲染不同样式
    switch (column.type) {
      case "status":
        return (
          <Chip
            label={value}
            size="small"
            color={getStatusColor(value)}
            variant="outlined"
            sx={{
              borderRadius: "12px",
              fontSize: "12px",
              fontWeight: 500,
            }}
          />
        );
      case "avatar":
        return (
          <Avatar src={value} sx={{ width: 32, height: 32 }}>
            {row.name?.charAt(0)}
          </Avatar>
        );
      case "actions":
        return (
          <Box sx={{ display: "flex", gap: 0.5 }}>
            {actions.map((action, index) => (
              <Tooltip key={index} title={action.tooltip || action.label}>
                <IconButton
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation();
                    action.onClick && action.onClick(row);
                  }}
                  sx={{
                    color: action.color || "inherit",
                    "&:hover": {
                      backgroundColor: `${
                        action.color || theme.palette.primary.main
                      }15`,
                    },
                  }}>
                  {action.icon}
                </IconButton>
              </Tooltip>
            ))}
          </Box>
        );
      default:
        return (
          <Typography
            variant="body2"
            sx={{
              color: theme.palette.text.primary,
              fontWeight: column.fontWeight || 400,
            }}>
            {value || "-"}
          </Typography>
        );
    }
  };

  // 获取状态颜色
  const getStatusColor = (status) => {
    const statusMap = {
      active: "success",
      inactive: "default",
      pending: "warning",
      error: "error",
      success: "success",
    };
    return statusMap[status?.toLowerCase()] || "default";
  };

  const isSelected = (id) => selectedRows.indexOf(id) !== -1;
  const numSelected = selectedRows.length;
  const rowCount = rows.length;

  if (loading) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: 200,
        }}>
        <CircularProgress />
      </Box>
    );
  }

  if (rows.length === 0 && !loading) {
    return (
      <Paper
        sx={{
          p: 4,
          textAlign: "center",
          backgroundColor: theme.palette.background.paper,
          borderRadius: 2,
          border: `1px solid ${theme.palette.divider}`,
        }}>
        <NoResultFound message={emptyMessage || t("tips.no_record_found")} />
      </Paper>
    );
  }

  return (
    <Paper
      sx={{
        width: "100%",
        backgroundColor: theme.palette.background.paper,
        borderRadius: 2,
        border: `1px solid ${theme.palette.divider}`,
        overflow: "hidden",
        boxShadow: "0px 2px 8px rgba(0, 0, 0, 0.05)",
      }}>
      <TableContainer sx={{ maxHeight }}>
        <Table
          stickyHeader={stickyHeader}
          size={dense ? "small" : "medium"}
          sx={{
            "& .MuiTableCell-root": {
              borderBottom: `1px solid ${theme.palette.divider}`,
            },
          }}>
          <TableHead>
            <TableRow
              sx={{
                backgroundColor: "#F8F9FA",
                "& .MuiTableCell-head": {
                  backgroundColor: "#F8F9FA",
                  color: theme.palette.text.secondary,
                  fontWeight: 600,
                  fontSize: "13px",
                  textTransform: "uppercase",
                  letterSpacing: "0.5px",
                  padding: dense ? "8px 16px" : "16px",
                  borderBottom: `2px solid ${theme.palette.divider}`,
                },
              }}>
              {checkboxSelection && (
                <TableCell padding="checkbox">
                  <Checkbox
                    color="primary"
                    indeterminate={numSelected > 0 && numSelected < rowCount}
                    checked={rowCount > 0 && numSelected === rowCount}
                    onChange={handleSelectAllClick}
                    size="small"
                  />
                </TableCell>
              )}
              {columns.map((column) => (
                <TableCell
                  key={column.field}
                  align={column.align || "left"}
                  style={{ minWidth: column.minWidth }}
                  sortDirection={orderBy === column.field ? order : false}>
                  {sortable && column.sortable !== false ? (
                    <TableSortLabel
                      active={orderBy === column.field}
                      direction={orderBy === column.field ? order : "asc"}
                      onClick={() => handleSort(column.field)}
                      sx={{
                        "& .MuiTableSortLabel-icon": {
                          color: `${theme.palette.primary.main} !important`,
                        },
                      }}>
                      {column.headerName || column.field}
                    </TableSortLabel>
                  ) : (
                    column.headerName || column.field
                  )}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {loading && showSkeleton
              ? renderSkeleton()
              : rows.map((row, index) => {
                  const isItemSelected = isSelected(row.id);
                  const labelId = `enhanced-table-checkbox-${index}`;

                  return (
                    <TableRow
                      hover
                      key={row.id}
                      selected={isItemSelected}
                      onClick={() => onRowClick && onRowClick(row)}
                      onMouseEnter={() => setHoveredRow(row.id)}
                      onMouseLeave={() => setHoveredRow(null)}
                      sx={{
                        cursor: onRowClick ? "pointer" : "default",
                        "&:hover": {
                          backgroundColor: `${theme.palette.primary.main}08`,
                        },
                        "&.Mui-selected": {
                          backgroundColor: `${theme.palette.primary.main}12`,
                          "&:hover": {
                            backgroundColor: `${theme.palette.primary.main}20`,
                          },
                        },
                        "& .MuiTableCell-root": {
                          padding: dense ? "8px 16px" : "12px 16px",
                          borderBottom: `1px solid ${theme.palette.divider}`,
                        },
                      }}>
                      {checkboxSelection && (
                        <TableCell padding="checkbox">
                          <Checkbox
                            color="primary"
                            checked={isItemSelected}
                            onChange={(event) => handleRowSelect(event, row.id)}
                            size="small"
                            inputProps={{
                              "aria-labelledby": labelId,
                            }}
                          />
                        </TableCell>
                      )}
                      {columns.map((column) => (
                        <TableCell
                          key={column.field}
                          align={column.align || "left"}>
                          {renderCellContent(column, row)}
                        </TableCell>
                      ))}
                    </TableRow>
                  );
                })}
          </TableBody>
        </Table>
      </TableContainer>

      {pagination && (
        <TablePagination
          rowsPerPageOptions={rowsPerPageOptions}
          component="div"
          count={totalRecords}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={onPageChange}
          onRowsPerPageChange={onRowsPerPageChange}
          labelRowsPerPage={t("common.per_page")}
          labelDisplayedRows={({ from, to, count }) =>
            `${from}-${to} ${t("common.of")} ${
              count !== -1 ? count : `${t("common.more_than")} ${to}`
            }`
          }
          sx={{
            borderTop: `1px solid ${theme.palette.divider}`,
            backgroundColor: "#FAFBFC",
            "& .MuiTablePagination-toolbar": {
              paddingLeft: 2,
              paddingRight: 1,
            },
            "& .MuiTablePagination-selectLabel, & .MuiTablePagination-displayedRows":
              {
                fontSize: "14px",
                color: theme.palette.text.secondary,
              },
            "& .MuiTablePagination-select": {
              fontSize: "14px",
            },
          }}
        />
      )}
    </Paper>
  );
};

export default EnhancedDataTable;
